0000000000000000000000000000000000000000 6daa69d06bb168b37d360b87113d2cb12807430d <PERSON> <<EMAIL>> 1742394689 +0000	clone: from github.com:Sababuu/backend-main.git
6daa69d06bb168b37d360b87113d2cb12807430d cdbcf647b095a7a4217241d92cae23364de2d35c <PERSON> <<EMAIL>> 1742419971 +0000	checkout: moving from prod to stage
cdbcf647b095a7a4217241d92cae23364de2d35c cdbcf647b095a7a4217241d92cae23364de2d35c <PERSON> <<EMAIL>> 1742419993 +0000	checkout: moving from stage to jakob/sab-753-fix-github-pr-descriptionlinear-comment-image-urls
cdbcf647b095a7a4217241d92cae23364de2d35c 5a5994d95cd33462713c68dfe52528a94a5dcfca <PERSON> <<EMAIL>> 1742419996 +0000	commit: Added repo context to "What did you do" template
5a5994d95cd33462713c68dfe52528a94a5dcfca 640d475a6e1a02a9fb81c76febc00627c397bd45 Jakob Barnwell <<EMAIL>> 1742420013 +0000	commit: Fix Linear comment image URLs
640d475a6e1a02a9fb81c76febc00627c397bd45 74e986a4f6e826111a6ef947fba72599ec4211f8 Jakob Barnwell <<EMAIL>> 1742560331 +0000	commit: Update backend_posthog_annotations.yml
74e986a4f6e826111a6ef947fba72599ec4211f8 70f7da21a02c8d355c81c8795de188ea43f6fbd9 Jakob Barnwell <<EMAIL>> 1743071086 +0100	checkout: moving from jakob/sab-753-fix-github-pr-descriptionlinear-comment-image-urls to stage
70f7da21a02c8d355c81c8795de188ea43f6fbd9 74979a24fedc9e2ebb77d5d3133c9050f24f58a0 Jakob Barnwell <<EMAIL>> 1743071705 +0100	commit: Fix to Org secrets for PR workflow
74979a24fedc9e2ebb77d5d3133c9050f24f58a0 fe6deff28c44982668c6debc9ad4805e7b9fd352 Jakob Barnwell <<EMAIL>> 1743426025 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
fe6deff28c44982668c6debc9ad4805e7b9fd352 0bbd21d2c9e961c211ab3489a5c59120266bac75 Jakob Barnwell <<EMAIL>> 1743445119 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
0bbd21d2c9e961c211ab3489a5c59120266bac75 0bbd21d2c9e961c211ab3489a5c59120266bac75 Jakob Barnwell <<EMAIL>> 1743446151 +0200	checkout: moving from stage to jakob/sab-762-fix-bug-unable-to-top-up-on-staging-environment
0bbd21d2c9e961c211ab3489a5c59120266bac75 7795de08b9e6a10c0a456af6f248099d258241c9 Jakob Barnwell <<EMAIL>> 1743446257 +0200	commit: Update PawaPayClient.php with skips for stage env
7795de08b9e6a10c0a456af6f248099d258241c9 dd6f4e9bc4c6cc96430f5f011eee3e657f8826b1 Jakob Barnwell <<EMAIL>> 1743506708 +0200	checkout: moving from jakob/sab-762-fix-bug-unable-to-top-up-on-staging-environment to stage
dd6f4e9bc4c6cc96430f5f011eee3e657f8826b1 6cf544000b962a92d7ab3ea8209748464b069faa Jakob Barnwell <<EMAIL>> 1743506772 +0200	commit: Hotfix to using proper env checks in PawaPayClient.php
6cf544000b962a92d7ab3ea8209748464b069faa 61b75e45000031d3f55f2dfd9c0a29b1b0b88262 Jakob Barnwell <<EMAIL>> 1743510095 +0200	commit: Hotfix to not skip correspondent checker in deposit
61b75e45000031d3f55f2dfd9c0a29b1b0b88262 5325ebd82eb4a59fd97b7c7af0371f733e4f3abd Jakob Barnwell <<EMAIL>> 1743600451 +0200	pull: Fast-forward
5325ebd82eb4a59fd97b7c7af0371f733e4f3abd 9d113882a43aa0b858501cc58313227f34291713 Jakob Barnwell <<EMAIL>> 1743602117 +0200	commit: Updates to migrations to work on fresh install
9d113882a43aa0b858501cc58313227f34291713 3b311db2ed49f01898b21c9d01cde0f906685947 Jakob Barnwell <<EMAIL>> 1743694495 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
3b311db2ed49f01898b21c9d01cde0f906685947 3b311db2ed49f01898b21c9d01cde0f906685947 Jakob Barnwell <<EMAIL>> 1743711443 +0200	checkout: moving from stage to jakob/sab-767-show-old-verification-photos-for-users-that-verified-before
3b311db2ed49f01898b21c9d01cde0f906685947 9927dc0e5e414ecfa79c89947536ae76dc8f76ee Jakob Barnwell <<EMAIL>> 1743711490 +0200	commit: Try and fetch verification photos from before profileid->userid
9927dc0e5e414ecfa79c89947536ae76dc8f76ee 3b311db2ed49f01898b21c9d01cde0f906685947 Jakob Barnwell <<EMAIL>> 1743711752 +0200	checkout: moving from jakob/sab-767-show-old-verification-photos-for-users-that-verified-before to stage
3b311db2ed49f01898b21c9d01cde0f906685947 3b311db2ed49f01898b21c9d01cde0f906685947 Jakob Barnwell <<EMAIL>> 1743711815 +0200	reset: moving to HEAD
3b311db2ed49f01898b21c9d01cde0f906685947 9927dc0e5e414ecfa79c89947536ae76dc8f76ee Jakob Barnwell <<EMAIL>> 1743711816 +0200	checkout: moving from stage to jakob/sab-767-show-old-verification-photos-for-users-that-verified-before
9927dc0e5e414ecfa79c89947536ae76dc8f76ee 3b311db2ed49f01898b21c9d01cde0f906685947 Jakob Barnwell <<EMAIL>> 1743711951 +0200	checkout: moving from jakob/sab-767-show-old-verification-photos-for-users-that-verified-before to stage
3b311db2ed49f01898b21c9d01cde0f906685947 9927dc0e5e414ecfa79c89947536ae76dc8f76ee Jakob Barnwell <<EMAIL>> 1743712530 +0200	checkout: moving from stage to jakob/sab-767-show-old-verification-photos-for-users-that-verified-before
9927dc0e5e414ecfa79c89947536ae76dc8f76ee 3b311db2ed49f01898b21c9d01cde0f906685947 Jakob Barnwell <<EMAIL>> 1743712562 +0200	checkout: moving from jakob/sab-767-show-old-verification-photos-for-users-that-verified-before to stage
3b311db2ed49f01898b21c9d01cde0f906685947 7aaa4b436e8f1977ee6885b96be48189e6836667 Jakob Barnwell <<EMAIL>> 1743773390 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
7aaa4b436e8f1977ee6885b96be48189e6836667 9927dc0e5e414ecfa79c89947536ae76dc8f76ee Jakob Barnwell <<EMAIL>> 1743773407 +0200	checkout: moving from stage to jakob/sab-767-show-old-verification-photos-for-users-that-verified-before
9927dc0e5e414ecfa79c89947536ae76dc8f76ee 281b2b0b558bfdda5aaafa9e84cd5aacf9b595e8 Jakob Barnwell <<EMAIL>> 1743774789 +0200	commit: Update to using exists()
281b2b0b558bfdda5aaafa9e84cd5aacf9b595e8 7aaa4b436e8f1977ee6885b96be48189e6836667 Jakob Barnwell <<EMAIL>> 1743775132 +0200	checkout: moving from jakob/sab-767-show-old-verification-photos-for-users-that-verified-before to stage
7aaa4b436e8f1977ee6885b96be48189e6836667 7aaa4b436e8f1977ee6885b96be48189e6836667 Jakob Barnwell <<EMAIL>> 1743777473 +0200	checkout: moving from stage to jakob/sab-764-fix-the-deployment-slack-notification
7aaa4b436e8f1977ee6885b96be48189e6836667 448fab27639b648df65882559a976fad6eb9461b Jakob Barnwell <<EMAIL>> 1743777478 +0200	commit: Update to deployment jobs
448fab27639b648df65882559a976fad6eb9461b 7aaa4b436e8f1977ee6885b96be48189e6836667 Jakob Barnwell <<EMAIL>> 1743780022 +0200	checkout: moving from jakob/sab-764-fix-the-deployment-slack-notification to stage
7aaa4b436e8f1977ee6885b96be48189e6836667 eab82da99331ba560c26e25c231a752ca4aa26ca Jakob Barnwell <<EMAIL>> 1744033369 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
eab82da99331ba560c26e25c231a752ca4aa26ca b092540c04fb7c3f8cd8ef5597941160e8fb39d3 Jakob Barnwell <<EMAIL>> 1744033807 +0200	commit: Hotfix for admin panel nextUrl
b092540c04fb7c3f8cd8ef5597941160e8fb39d3 ade3c1e30f7c2ee25aba79b97fa1f89e086e212f Jakob Barnwell <<EMAIL>> 1744137037 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
ade3c1e30f7c2ee25aba79b97fa1f89e086e212f ade3c1e30f7c2ee25aba79b97fa1f89e086e212f Jakob Barnwell <<EMAIL>> 1744137093 +0200	checkout: moving from stage to jakob/sab-784-update-survey-functionality
ade3c1e30f7c2ee25aba79b97fa1f89e086e212f c8613ef2949439b88df1b6f2a69799210f3f0e53 Jakob Barnwell <<EMAIL>> 1744137095 +0200	commit: Backend update to SurveyService.php
c8613ef2949439b88df1b6f2a69799210f3f0e53 ade3c1e30f7c2ee25aba79b97fa1f89e086e212f Jakob Barnwell <<EMAIL>> 1744277329 +0200	checkout: moving from jakob/sab-784-update-survey-functionality to stage
ade3c1e30f7c2ee25aba79b97fa1f89e086e212f c8613ef2949439b88df1b6f2a69799210f3f0e53 Jakob Barnwell <<EMAIL>> 1744284103 +0200	checkout: moving from stage to jakob/sab-784-update-survey-functionality
c8613ef2949439b88df1b6f2a69799210f3f0e53 3eba5f4b54ad1db4d46cccdc8802fc5e270c5205 Jakob Barnwell <<EMAIL>> 1744288851 +0200	commit: New rewardForSurvey method in WalletService
3eba5f4b54ad1db4d46cccdc8802fc5e270c5205 e91f4f9b034745f417bbe4dc1613e4be3de5b813 Jakob Barnwell <<EMAIL>> 1744311041 +0200	checkout: moving from jakob/sab-784-update-survey-functionality to stage
e91f4f9b034745f417bbe4dc1613e4be3de5b813 e91f4f9b034745f417bbe4dc1613e4be3de5b813 Jakob Barnwell <<EMAIL>> 1744460228 +0200	checkout: moving from stage to jakob/sab-790-add-backend-scam-alert-to-slack-that-checks-suspicious-user
e91f4f9b034745f417bbe4dc1613e4be3de5b813 319fcada93272fc74736739aac8b801b63b727b0 Jakob Barnwell <<EMAIL>> 1744460233 +0200	commit: New Scam Alert Slack feature
319fcada93272fc74736739aac8b801b63b727b0 e91f4f9b034745f417bbe4dc1613e4be3de5b813 Jakob Barnwell <<EMAIL>> 1744464707 +0200	checkout: moving from jakob/sab-790-add-backend-scam-alert-to-slack-that-checks-suspicious-user to stage
e91f4f9b034745f417bbe4dc1613e4be3de5b813 c458f7ff5667be08cc24114c43e3b7f7dbec200f Jakob Barnwell <<EMAIL>> 1744721814 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
c458f7ff5667be08cc24114c43e3b7f7dbec200f 319fcada93272fc74736739aac8b801b63b727b0 Jakob Barnwell <<EMAIL>> ********** +0200	checkout: moving from stage to jakob/sab-790-add-backend-scam-alert-to-slack-that-checks-suspicious-user
319fcada93272fc74736739aac8b801b63b727b0 d14ea59f8817c223bc37d76628e711e388b8a1b2 Jakob Barnwell <<EMAIL>> ********** +0200	commit: Use config() instead of env() everywhere
d14ea59f8817c223bc37d76628e711e388b8a1b2 6edda1653e81b91666d2e88712fbf9eb8d554cc2 Jakob Barnwell <<EMAIL>> ********** +0200	commit: New SlackClient and delete redundant provider
6edda1653e81b91666d2e88712fbf9eb8d554cc2 c458f7ff5667be08cc24114c43e3b7f7dbec200f Jakob Barnwell <<EMAIL>> ********** +0200	checkout: moving from jakob/sab-790-add-backend-scam-alert-to-slack-that-checks-suspicious-user to stage
c458f7ff5667be08cc24114c43e3b7f7dbec200f 6edda1653e81b91666d2e88712fbf9eb8d554cc2 Jakob Barnwell <<EMAIL>> ********** +0200	checkout: moving from stage to jakob/sab-790-add-backend-scam-alert-to-slack-that-checks-suspicious-user
6edda1653e81b91666d2e88712fbf9eb8d554cc2 767cd45f9554a28c8d015b44ff71b963be82237e Jakob Barnwell <<EMAIL>> ********** +0200	commit: using env(infra_env) and cleaning up services
767cd45f9554a28c8d015b44ff71b963be82237e f3c241074eb9e1993cf55f5958287ce14c85350d Jakob Barnwell <<EMAIL>> ********** +0200	commit: Use new SlackClient in staging_sms flow too
f3c241074eb9e1993cf55f5958287ce14c85350d c458f7ff5667be08cc24114c43e3b7f7dbec200f Jakob Barnwell <<EMAIL>> ********** +0200	checkout: moving from jakob/sab-790-add-backend-scam-alert-to-slack-that-checks-suspicious-user to stage
c458f7ff5667be08cc24114c43e3b7f7dbec200f 71659340074d1db609205c6d0d7c6fdf55520562 Jakob Barnwell <<EMAIL>> 1744809065 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
71659340074d1db609205c6d0d7c6fdf55520562 cdaac911c387d16657dbb460afec0f9818cbba81 Jakob Barnwell <<EMAIL>> 1744816686 +0200	checkout: moving from stage to danil/sab-800-update-backend-code-for-custom-liking-and-feed
cdaac911c387d16657dbb460afec0f9818cbba81 639d99cfd252664ca50973681240344cbc825f4b Jakob Barnwell <<EMAIL>> 1744891108 +0200	checkout: moving from danil/sab-800-update-backend-code-for-custom-liking-and-feed to danil/sab-797-update-chat-pre-filling-logic
639d99cfd252664ca50973681240344cbc825f4b 16778e26ebb014e6ca1b6fa08f83d48275ea0589 Jakob Barnwell <<EMAIL>> 1744892728 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
16778e26ebb014e6ca1b6fa08f83d48275ea0589 126dabc694d1e957dceb77a60b4cefba18c60208 Jakob Barnwell <<EMAIL>> 1744893131 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
126dabc694d1e957dceb77a60b4cefba18c60208 4b4e1db24066de7fd641a58182e227d3801abe2e Jakob Barnwell <<EMAIL>> 1744893275 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
4b4e1db24066de7fd641a58182e227d3801abe2e 2a4e0aaa63f6045b9d2dc2d65481741c907ba72b Jakob Barnwell <<EMAIL>> 1744901116 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
2a4e0aaa63f6045b9d2dc2d65481741c907ba72b 93238a649f172b0d91c959f1ee14beb204cee8fa Jakob Barnwell <<EMAIL>> 1744916576 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
93238a649f172b0d91c959f1ee14beb204cee8fa 2ef6457dc06c616aac4ae5582153114836b5e498 Jakob Barnwell <<EMAIL>> 1744967971 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
2ef6457dc06c616aac4ae5582153114836b5e498 2255ade4798d27b5ecef2b9aa06c8c65046e2da9 Jakob Barnwell <<EMAIL>> 1745074074 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
2255ade4798d27b5ecef2b9aa06c8c65046e2da9 56c2e9e10acbee13f1b2578caa6a5d9bd2c1414c Jakob Barnwell <<EMAIL>> 1745256229 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
56c2e9e10acbee13f1b2578caa6a5d9bd2c1414c e9ea7d19c9c235521b5723e495fb4125b8bfc9d1 Jakob Barnwell <<EMAIL>> 1745317748 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
e9ea7d19c9c235521b5723e495fb4125b8bfc9d1 26fb99ce747619b0e04875114c681bd55d4c41cf Jakob Barnwell <<EMAIL>> 1745338063 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
26fb99ce747619b0e04875114c681bd55d4c41cf 71659340074d1db609205c6d0d7c6fdf55520562 Jakob Barnwell <<EMAIL>> 1746433628 +0200	checkout: moving from danil/sab-797-update-chat-pre-filling-logic to stage
71659340074d1db609205c6d0d7c6fdf55520562 e48c352e026fe73a70a20cfcbc059efd3ac7cb0c Jakob Barnwell <<EMAIL>> 1746433632 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
e48c352e026fe73a70a20cfcbc059efd3ac7cb0c da99053553062807de55166343487f0dd46d0e4e Jakob Barnwell <<EMAIL>> 1746456747 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
da99053553062807de55166343487f0dd46d0e4e eab4beb4b89108304b3e6436e0dee97e8cd535b5 Jakob Barnwell <<EMAIL>> 1746696957 +0200	commit: revert migration file changes
eab4beb4b89108304b3e6436e0dee97e8cd535b5 75a0499f743a64db16e2c53345eabe1b0276647d Jakob Barnwell <<EMAIL>> 1747124959 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
75a0499f743a64db16e2c53345eabe1b0276647d f3e5e13175e26f683e69c2777c082e91c80f00b5 Jakob Barnwell <<EMAIL>> 1747391812 +0100	pull --ff --recurse-submodules --progress origin: Fast-forward
f3e5e13175e26f683e69c2777c082e91c80f00b5 3b2e37a0328f9af4747e383f4bb815c34d98f0e0 Jakob Barnwell <<EMAIL>> 1747822098 +0100	pull --ff --recurse-submodules --progress origin: Fast-forward
3b2e37a0328f9af4747e383f4bb815c34d98f0e0 856f4d2155449a2b9c52a9c4d8b07dce78a24870 Jakob Barnwell <<EMAIL>> 1747839891 +0100	pull --ff --recurse-submodules --progress origin: Fast-forward
856f4d2155449a2b9c52a9c4d8b07dce78a24870 856f4d2155449a2b9c52a9c4d8b07dce78a24870 Jakob Barnwell <<EMAIL>> 1747862352 +0100	checkout: moving from stage to jakob/sab-776-pass-mteja-calling-history-to-posthog-via-webhook
856f4d2155449a2b9c52a9c4d8b07dce78a24870 b6db4d6821897bab92f367fda1649ab7128d0a54 Jakob Barnwell <<EMAIL>> 1747862353 +0100	commit: Handle Mteja call logs and send them to posthog
b6db4d6821897bab92f367fda1649ab7128d0a54 242f19d5c6a525e4e8e8f39975bed5f8acdc7ca2 Jakob Barnwell <<EMAIL>> 1747862840 +0100	commit: revert changes to PostHogService
242f19d5c6a525e4e8e8f39975bed5f8acdc7ca2 5a2dd4a6b2b120ea1ca09de03277adacf39c57ea Jakob Barnwell <<EMAIL>> 1748421769 +0200	checkout: moving from jakob/sab-776-pass-mteja-calling-history-to-posthog-via-webhook to stage
5a2dd4a6b2b120ea1ca09de03277adacf39c57ea d0de09830060c1fcf42feb1f6aaf05fff9a8153f Jakob Barnwell <<EMAIL>> 1748455496 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
