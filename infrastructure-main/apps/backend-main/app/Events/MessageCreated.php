<?php

declare(strict_types=1);

namespace App\Events;

use App\Http\Resources\Dialog\DialogResource;
use App\Http\Resources\Dialog\MessageResource;
use App\Models\Dialog;
use App\Models\Message;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class MessageCreated implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(private readonly Message $message)
    {
    }

    /**
     * @return array<int, Channel>
     */
    public function broadcastOn(): array
    {
        if (!$this->message->recipient) {
            return [];
        }

        return [
            new PrivateChannel('User.' . $this->message->recipient->id),
        ];
    }

    public function broadcastWith(): array
    {
        return [
            'message' => MessageResource::make($this->message),
            'dialog' => DialogResource::make($this->message->dialog),
        ];
    }

    public function broadcastAs(): string
    {
        return 'Message';
    }
}
