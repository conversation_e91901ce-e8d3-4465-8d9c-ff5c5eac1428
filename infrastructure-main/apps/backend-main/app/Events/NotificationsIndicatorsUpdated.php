<?php

declare(strict_types=1);

namespace App\Events;

use App\Models\User;
use App\Services\User\NotificationsService;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class NotificationsIndicatorsUpdated implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    private NotificationsService $notificationsService;

    public function __construct(private readonly User $user)
    {
        $this->notificationsService = new NotificationsService($this->user);
    }

    /**
     * @return array<int, Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('User.' . $this->user->id),
        ];
    }

    public function broadcastWith(): array
    {
        return [
            'new_messages_notifications' => $this->notificationsService->countNewMessages(),
            'new_likes_notifications' => $this->notificationsService->countNewLikes(),
            'new_matches_notifications' =>  $this->notificationsService->countNewMatches(),
        ];
    }

    public function broadcastAs(): string
    {
        return 'NotificationsIndicators';
    }
}
