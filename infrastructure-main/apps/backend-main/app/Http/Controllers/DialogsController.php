<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\User\ActionType;
use App\Events\DialogUpdated;
use App\Exceptions\Dialog\DialogAlreadyExistException;
use App\Exceptions\User\BoostUsageTrackingException;
use App\Exceptions\Wallet\NotEnoughCreditsException;
use App\Http\Requests\InstaChatRequest;
use App\Http\Resources\Dialog\DialogResource;
use App\Models\Dialog;
use App\Models\User;
use App\Notifications\NewMatches;
use App\Notifications\NewMessage;
use App\Services\User\BoostTrackingService;
use App\Services\Dialog\DialogService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class DialogsController extends Controller
{
    public function __construct(
        private readonly DialogService $dialogService
    ) {}

    /**
     * @throws NotEnoughCreditsException
     * @throws DialogAlreadyExistException
     * @throws BoostUsageTrackingException
     * @throws \JsonException
     */
    public function instachat(
        User $user,
        InstaChatRequest $request
    ): JsonResponse {
        $fromBoosted = $request->boolean("from_boosted");

        if ($fromBoosted) {
            $boostTrackingService = new BoostTrackingService(
                $request->user(),
                $user
            );
            $boostTrackingService->ensureActiveBoostPackage(
                ActionType::INSTACHAT
            );
        }

        $dialog = $this->dialogService->instachat(user(), $user, $fromBoosted);

        if ($fromBoosted) {
            $boostTrackingService->trackProfileBoostUsage(
                ActionType::INSTACHAT
            );
        }

        return $this->successWithData([
            "dialog" => DialogResource::make($dialog),
        ]);
    }

    public function block(Dialog $dialog): Response
    {
        $this->dialogService->blockDialog($dialog, user());

        return $this->successWithEmptyBody();
    }

    public function list()
    {
        $dialogs = $this->dialogService->getDialogs();

        return $this->successWithData([
            "dialogs" => DialogResource::collection($dialogs->items()),
            "currentPage" => $dialogs->currentPage(),
            "hasMorePages" => $dialogs->hasMorePages(),
        ]);
    }

    public function showById(Dialog $dialog)
    {
        return $this->successWithData([
            "dialog" => DialogResource::make($dialog),
        ]);
    }

    public function delete(Request $request, Dialog $dialog): Response
    {
        $dialog->deleted_by_user_id = $request->user()->id;
        $dialog->save();
        $dialog->delete();
        return $this->successWithEmptyBody();
    }

    public function readMessagesOnDialog(Dialog $dialog): Response
    {
        user()->markAsReadNotifications(
            [NewMessage::class],
            $dialog->id
        );
        return $this->successWithEmptyBody();
    }
}
