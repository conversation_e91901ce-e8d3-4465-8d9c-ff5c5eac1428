<?php

namespace App\Http\Controllers;

use App\Http\Requests\SubscribeToNotificationsRequest;
use App\Notifications\PushNotification;
use App\Services\User\NotificationsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class NotificationManagerController extends Controller
{
    public function subscribeToPushNotifications(SubscribeToNotificationsRequest $request): JsonResponse
    {
        $user = $request->user();
        $endpoint = $request->get('endpoint');
        $key = $request->get('key');
        $token = $request->get('token');
        $contentEncoding = $request->get('contentEncoding');

        $newSubscription = $user->updatePushSubscription($endpoint, $key, $token, $contentEncoding);

        return $this->successWithData([
            'subscription' => $newSubscription,
        ]);
    }

    public function unsubscribeFromPushNotifications(Request $request): Response
    {
        $user = $request->user();
        $endpoint = $request->get('endpoint');
        $user->deletePushSubscription($endpoint);

        return $this->successWithEmptyBody();
    }

    public function getNotificationIndicators(): JsonResponse
    {
        $notifications = new NotificationsService(user());

        return $this->successWithData([
            'new_messages_count' => $notifications->countNewMessages(),
            'new_likes_count' => $notifications->countNewLikes(),
            'new_matches_count' => $notifications->countNewMatches(),
        ]);
    }

    /**
     * Just a testing endpoint
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function send(Request $request): JsonResponse
    {
        $user = $request->user();
        $result = $user->notify(new PushNotification());

        return $this->successWithData([
            'data' => $result,
        ]);
    }
}
