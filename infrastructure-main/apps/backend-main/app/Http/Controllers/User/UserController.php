<?php

declare(strict_types=1);

namespace App\Http\Controllers\User;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Services\User\UserUpdater;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Requests\UpdatePhoneRequest;
use App\Http\Requests\UpdateOneSignalRequest;
use App\Http\Resources\User\GuestUserResource;
use App\Http\Resources\User\OwnUserResource;
use App\Models\User;
use App\Rules\PhoneCountry;
use App\Services\Auth\PhoneVerificationService;

class UserController extends Controller
{
    public function delete(Request $request): Response
    {
        $request->user()->delete();

        return $this->successWithEmptyBody();
    }

    public function update(UpdateUserRequest $request): JsonResponse
    {
        $userUpdater = new UserUpdater(
            user(),
            $request->only([
                "name",
                "birthday",
                "location",
                "description",
                "latitude",
                "longitude",
            ]),
            $request->string("avatar")->toString()
        );

        $userUpdater->update();

        return $this->successWithData([
            "user" => OwnUserResource::make(
                user()->load(["gallery", "communities", "mainImage"])
            ),
        ]);
    }

    public function updatePhone(UpdatePhoneRequest $request, PhoneVerificationService $verificationService): JsonResponse
    {
        $user = user();
        $verificationService->verify($request->get("phone"), $request->get("code"));
        $user->phone = $request->get("phone");

        $user->save();
        UserUpdater::updatePhoneCorrespondent($user);
        return $this->successWithData([
            "user" => OwnUserResource::make($user),
        ]);
    }

    public function updateOneSignal(UpdateOneSignalRequest $request): Response
    {
        $user = $request->user();

        $user->oneSignalSubscription()->updateOrCreate(
            [],
            [
                "token_id" => $user->currentAccessToken()->id,
                "subscription_id" => $request->get("subscription_id"),
            ]
        );

        return $this->successWithEmptyBody();
    }

    public function deleteOneSignal(Request $request): Response
    {
        $user = $request->user();
        $user->oneSignalSubscription()->delete();
        return $this->successWithEmptyBody();
    }

    public function sendPhoneVerification(Request $request, PhoneVerificationService $verificationService): Response
    {
        $request->validate([
            "phone" => ["required", "numeric", new PhoneCountry(), "unique:users,phone"],
        ]);

        $verificationService->sendSMS($request->get("phone"));

        return $this->successWithEmptyBody();
    }

    public function show(User $user): JsonResponse
    {
        return $this->successWithData([
            "user" => GuestUserResource::make($user->load(["gallery", "chosenProperties", "mainImage"])),
        ]);
    }

    public function ownProfile(): JsonResponse
    {
        return $this->successWithData([
            "user" => OwnUserResource::make(
                user()->load(["gallery", "communities", "chosenProperties"])
            ),

        ]);
    }

    public function heartBeat(): JsonResponse
    {
        $current_last_seen_at = user()->updateLastSeenAt()->last_seen_at;
        return $this->successWithData([
            "current_last_seen_at" => $current_last_seen_at,
        ]);
    }
}
