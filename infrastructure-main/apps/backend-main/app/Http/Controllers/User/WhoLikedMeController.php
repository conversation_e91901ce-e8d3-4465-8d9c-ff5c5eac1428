<?php

declare(strict_types=1);

namespace App\Http\Controllers\User;

use App\Exceptions\User\LikeAlreadyRevealedException;
use App\Exceptions\Wallet\NotEnoughCreditsException;
use App\Http\Controllers\Controller;
use App\Http\Requests\CursorRequest;
use App\Http\Resources\User\LikeResource;
use App\Http\Resources\User\FeedEntryResource;
use App\Models\Action;
use App\Notifications\NewLike;
use App\Services\Action\RevealLikeService;
use App\Services\User\UserExplorer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WhoLikedMeController extends Controller
{
    public function feed(UserExplorer $userExplorer, Request $request): JsonResponse
    {
        $withoutFilters = $request->boolean('withoutFilters', false);
        $feedEntries = $userExplorer->setUser(user())->getFeedPaginator($withoutFilters);

        $withIsDislikedFlag = $userExplorer->augmentIsDislikedFlag(collect($feedEntries->items()));
        user()->markAsReadNotifications([NewLike::class]);
        return $this->successWithData([
            'likes' => FeedEntryResource::collection($withIsDislikedFlag),
            'last_page' => $feedEntries->lastPage(),
            'hasMorePages' => $feedEntries->hasMorePages(),
        ]);
    }

    /**
     * @throws NotEnoughCreditsException
     * @throws \JsonException
     * @throws LikeAlreadyRevealedException
     */
    public function reveal(Action $action, Request $request, RevealLikeService $revealLikeService): JsonResponse
    {
        $userResource = $revealLikeService->performReveal($request->user()->wallet, $action);

        return $this->successWithData([
            'user' => $userResource
        ]);
    }
}
