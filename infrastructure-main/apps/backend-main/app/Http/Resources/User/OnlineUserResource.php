<?php

declare(strict_types=1);

namespace App\Http\Resources\User;

use App\Enums\User\ActionType;
use App\Models\User;
use Illuminate\Http\Request;

/**
 * @mixin User
 */
class OnlineUserResource extends BaseUserResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $actionType = $this->receivedActions->first()?->type;
        $dialogId = $this->allDialogsRecipient->first()?->id ?? $this->allDialogsSender->first()?->id;

        return [
            'id' => $this->id,
            'person_verification' => $this->isVerified(),
            'name' => $this->name,
            'actionable' => (!$actionType || $actionType !== ActionType::DISLIKE) && !$dialogId,
            'actionType' => $actionType,
            'dialog_id' => $dialogId ?? null,
            'numberOfImages' => 1 + $this->gallery->count(),
            'description' => $this->description,
            'age' => now()->diffInYears($this->birthday),
            'avatar' => $this->avatar,
            'last_seen_at' => $this->last_seen_at,
        ];
    }
}
