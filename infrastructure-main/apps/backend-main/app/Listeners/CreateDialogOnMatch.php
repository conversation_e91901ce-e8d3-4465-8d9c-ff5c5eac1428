<?php

namespace App\Listeners;

use App\Events\MatchLikesEvent;
use App\Notifications\NewMatches;
use App\Services\Dialog\DialogService;

class CreateDialogOnMatch
{
    /**
     * Create the event listener.
     */
    public function __construct(
        private readonly DialogService $dialogService
    ) {
        // ...
    }
    /**
     * Create unInitiated dialog on match
     */
    public function handle(MatchLikesEvent $event): void
    {
        $dialog = $this->dialogService->createDialog($event->sender, $event->recipient);

        $event->sender->createNotification(new NewMatches());
        $event->recipient->createNotification(new NewMatches());
    }
}
