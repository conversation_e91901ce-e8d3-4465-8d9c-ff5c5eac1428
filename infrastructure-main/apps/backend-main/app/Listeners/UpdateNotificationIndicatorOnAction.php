<?php

namespace App\Listeners;

use App\Events\NotificationsIndicatorsUpdated;
use Illuminate\Notifications\Events\NotificationSent;

class UpdateNotificationIndicatorOnAction
{
    /**
     * Handle the event.
     */
    public function handle(NotificationSent $event): void
    {
        if ($event->channel === "broadcast") {
            NotificationsIndicatorsUpdated::dispatch($event->notifiable);
        }
    }
}
