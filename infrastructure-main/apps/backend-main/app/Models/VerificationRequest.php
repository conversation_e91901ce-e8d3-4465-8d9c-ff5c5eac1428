<?php

namespace App\Models;

use App\Enums\Profile\Verify\VerificationMethod;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int user_id
 * @property ?int profile_id
 * @property string method TODO: change type to existing VerificationMethod enum and add it to $casts array. We'll also need to update code that rely on this field
 * @property string status TODO: change type to existing VerificationStatus enum and add it to $casts array. We'll also need to update code that rely on this field
 * @property User $user
 * @property ?Profile $profile
 */
class VerificationRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        "user_id",
        "profile_id",
        "method",
        "status",
        "created_at",
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function profile(): BelongsTo
    {
        return $this->belongsTo(Profile::class);
    }
}
