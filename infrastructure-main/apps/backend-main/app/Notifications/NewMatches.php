<?php

declare(strict_types=1);

namespace App\Notifications;

use NotificationChannels\WebPush\WebPushMessage;
use NotificationChannels\OneSignal\OneSignalMessage;

class NewMatches extends SababuuNotification
{
    public function __construct() {}

    public function toArray(object $notifiable): array
    {
        return [];
    }

    public function getMessage(): string
    {
        return 'You have received a new match on Sababuu! Click here to view it: https://sababuu.com/dialogs';
    }

    public function toWebPush($notifiable): WebPushMessage
    {
        return (new WebPushMessage())
            ->title('New Match!')
            ->body('You have received a new match on Sababuu!')
            ->icon('pwa-64x64.png')
            ->badge('pwa-64x64.png')
            ->action('View Matches', 'view_matches', 'pwa-64x64.png')
            ->data(['action' => 'view_matches']);
    }

    public function toOneSignal($notifiable): OneSignalMessage
    {
        return OneSignalMessage::create()
            ->setSubject("New Match! ✨")
            ->setBody("You have a match from a profile you liked!")
            ->setUrl(config("services.onesignal.push_notification_base_url") . "/dialogs")
            ->setGroup("matches", ["en" => "You have $[notif_count] new matches"]);
    }
}
