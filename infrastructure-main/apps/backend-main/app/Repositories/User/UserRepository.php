<?php

namespace App\Repositories\User;

use App\Enums\Profile\ActionCountGroup;
use App\Http\DataObjects\User\UserSettings;
use App\Models\BoostedProfile;
use App\Models\Image;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class UserRepository
{
    public const GROUPED_PROFILES_QUERY_LIMIT = 6;

    private function baseQuery(User $user): Builder
    {
        return User::query()
            ->whereNot("id", $user->id)
            ->whereNotNull("description") // users without description are not completely registered
            ->where("gender", $user->oppositeGender)
            ->where("country", $user->country)
            ->with(['chosenProperties', 'gallery', 'mainImage', 'boosts'])
            ->whereDoesntHave("allDialogsRecipient", function ($query) use ($user) {
                $query->where("user_id_sender", $user->id);
            })
            ->whereDoesntHave("allDialogsSender", function ($query) use ($user) {
                $query->where("user_id_recipient", $user->id);
            })
            ->whereDoesntHave("receivedActions", function ($query) use ($user) {
                $query->where("user_id_sender", $user->id);
            });
    }


    /**
     * Return 6 profiles from each action count group
     *
     * @param User $user user that currently exploring other profiles
     * @param array $oldIds user IDs to exclude from the results
     * @return Collection $results profiles found
     */
    public function usersToExplore(
        User $user,
        $oldIds = []
    ): Collection {
        $query = $this->baseQuery($user)->where("is_boosted", false);
        $queryWithFilters = $this->addFilters($query, $oldIds);

        $actionCountGroups = ActionCountGroup::cases();

        $unionQuery = null;

        foreach ($actionCountGroups as $group) {
            $groupQuery = DB::table("mv_profiles_meta_grouped as m")
                ->joinSub($queryWithFilters, "v", function ($join) {
                    $join->on("m.user_id", "=", "v.id");
                })
                ->where("m.action_count_group", "=", $group->value)
                ->orderBy("m.match_weight", "desc")
                ->orderBy("v.last_seen_at", "desc")
                ->select("v.*", "m.action_count_group")
                ->limit(self::GROUPED_PROFILES_QUERY_LIMIT);

            if ($unionQuery === null) {
                $unionQuery = $groupQuery;
            } else {
                $unionQuery->unionAll($groupQuery);
            }
        }

        $profiles = $unionQuery->get();
        $results = User::hydrate($profiles->toArray());
        $results->load(['chosenProperties', 'gallery', 'mainImage', 'boosts']);
        return $results;
    }

    /**
     * Get boosted users to explore.
     *
     * @param User $user
     * @param array<int,int> $oldIds
     * @return Collection
     */
    public function boostedUsersToExplore(User $user, $oldIds = []): Collection
    {
        $query = $this->baseQuery($user)->where("is_boosted", true)->with(['boosts']);
        $queryWithFilters = $this->addFilters($query, $oldIds);
        /** @var Collection<int,User> */
        $boostedUsers = $queryWithFilters
            ->take(2)
            ->inRandomOrder()
            ->get();

        foreach ($boostedUsers as $boostedUser) { // update user boost impressions
            $boostedProfileIds = $boostedUser->boosts->pluck('id');
            BoostedProfile::query()->whereIn('id', $boostedProfileIds)->increment('impressions_count');
        }

        return $boostedUsers;
    }

    /**
     * Get profiles within a specified distance from a given location.
     *
     * @param Builder $query
     * @param float $distance in kilometers
     * @return Builder
     */
    public function usersNearbyQuery(Builder $query, float $distance): Builder
    {
        $user = user();
        $latitude = $user->latitude;
        $longitude = $user->longitude;

        $latRange = ($distance + 0.1) / 111.045; // 1 degree latitude ~ 111.045 km
        $lonRange = ($distance + 0.1) / (111.045 * cos(deg2rad($latitude)));

        $minLat = $latitude - $latRange;
        $maxLat = $latitude + $latRange;
        $minLon = $longitude - $lonRange;
        $maxLon = $longitude + $lonRange;

        return $query
            ->select()
            ->whereBetween("latitude", [$minLat, $maxLat])
            ->whereBetween("longitude", [$minLon, $maxLon])
            ->selectRaw(
                "ST_Distance_Sphere(POINT(`longitude`,`latitude`), POINT(?, ?)) AS distance",
                [$user->longitude, $user->latitude]
            )
            ->having("distance", "<", $distance * 1000)
            ->orderBy("distance");
    }

    /**
     * Add filters to the query based on user settings.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param array<int,int> $oldIds
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function addFilters(Builder $query, $oldIds = []): Builder
    {
        /*
         * We must search with a distance so we awlays run
         * the usersNearbyQuery with the users set distance
         * or the default distance value
         */
        $query = $this->usersNearbyQuery(
            $query,
            (float) (user()->settings->distance ?? UserSettings::DEFAULT_DISTANCE)
        );
        $query->when(
            isset(user()->settings->min_age) && isset(user()->settings->max_age),
            fn(Builder $q) => $this->ageBetween(
                $q,
                user()->settings->min_age,
                user()->settings->max_age
            )
        );
        $query->when(
            count($oldIds),
            fn(Builder $q) => $q->whereNotIn("id", $oldIds)
        );
        $query->when(
            isset(user()->settings->only_community) &&
                user()->settings->only_community === true,
            fn(Builder $q) => $q->whereHas(
                "communities",
                fn(Builder $q) => $q
                    ->where("community_id", user()->communities->first()->id)
                    ->where("is_active", 1)
            )
        );
        $query->when(
            !isset(user()->settings->only_community) ||
                user()->settings->only_community === false,
            fn(Builder $q) => $q->where(
                fn(Builder $q) => $q
                    ->whereDoesntHave("communities")
                    ->orWhereHas(
                        "communities",
                        fn(Builder $q) => $q->where("is_active", 0)
                    )
            )
        );
        return $query;
    }

    public function ageBetween(
        Builder $query,
        int $ageMin,
        int $ageMax
    ): Builder {
        return $query
            ->whereYear("birthday", "<=", $this->getYear($ageMin))
            ->whereYear("birthday", ">=", $this->getYear($ageMax));
    }

    public function getYear(int $age): int
    {
        return (int) Carbon::now()->subYears($age)->format("Y");
    }

    public function changeAvatar(
        User $user,
        string $path,
        string $disk = "s3"
    ): void {
        $user->images()->updateOrCreate(
            [
                "main" => Image::IS_MAIN,
                "user_id" => $user->id,
            ],
            [
                "path" => $path,
                "disk" => $disk,
            ]
        );
    }
}
