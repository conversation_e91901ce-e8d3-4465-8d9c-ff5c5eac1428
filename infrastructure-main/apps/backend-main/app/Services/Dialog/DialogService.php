<?php

declare(strict_types=1);

namespace App\Services\Dialog;

use App\Constants\Events;
use App\Enums\User\ActionType;
use App\Exceptions\Dialog\DialogAlreadyExistException;
use App\Exceptions\Dialog\DialogIsBlockedException;
use App\Models\Action;
use App\Models\Dialog;
use App\Models\User;
use App\Notifications\NewMatches;
use App\Notifications\NewMessage;
use App\Services\PostHog\PosthogService;
use App\Services\Wallet\WalletService;
use Exception;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class DialogService
{
    private const PER_PAGE = 10;
    public function __construct(
        private readonly PosthogService $postHog,
        private readonly MessageService $messageService
    ) {}

    public function isDialogExists(User $sender, User $recipient): bool
    {
        return Dialog::query()
            ->where(['user_id_sender' => $sender->id, 'user_id_recipient' => $recipient->id])
            ->orWhere(function ($builder) use ($sender, $recipient) {
                $builder->where([
                    ['user_id_recipient', '=', $sender->id],
                    ['user_id_sender', '=', $recipient->id],
                ]);
            })
            ->exists();
    }

    public function createDialog(User $sender, User $recipient, bool $initiated = false, bool $instachat = false): Dialog
    {
        /** @var Dialog $dialog */
        $dialog = Dialog::query()->create([
            'user_id_sender' => $sender->id,
            'user_id_recipient' => $recipient->id,
            'instachat' => $instachat,
            'is_initiated' => $initiated
        ]);
        /** @var Collection<int,Action> $senderActions */
        $senderActions = $sender->actions()->where('user_id_recipient', $recipient->id)->likesAndGifts()->get();
        /** @var Collection<int,Action> $recipientActions */
        $recipientActions = $recipient->actions()->where('user_id_recipient', $sender->id)->likesAndGifts()->get();
        $allActions = $senderActions->merge($recipientActions);
        $allActions->each(function ($action) use ($dialog) {
            if ($action->type === ActionType::GIFT) {
                $this->messageService->createMessageWithGift($dialog, $action->gift, $action->sender);
            } else {
                $this->messageService->createMessageWithCompliment($dialog, $action, isGenerated: true);
            }
        });

        return $dialog->refresh();
    }

    public function instachat(User $sender, User $recipient, bool $fromBoosted)
    {
        if ($this->isDialogExists($sender, $recipient)) {
            throw new DialogAlreadyExistException();
        }
        if ($sender->isFromGhana()) {
            throw new DialogIsBlockedException();
        }
        try {
            DB::beginTransaction();
            if ($fromBoosted === false) {
                (new WalletService($sender->wallet))->buyInstachat($recipient->id);
            }
            $dialog = $this->createDialog($sender, $recipient, false, true);

            Action::query()->create([
                'type' => ActionType::INSTACHAT,
                'user_id_sender' => $sender->id,
                'user_id_recipient' => $recipient->id,
            ]);
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
        $this->postHog->logEvent(
            Events::INSTACHAT_RECEIVED,
            [
                'user_id' => $sender->id,
            ],
            $recipient->id,
        );
        $recipient->createNotification(new NewMatches());

        return $dialog;
    }

    public function blockDialog(Dialog $dialog, User $user): void
    {
        $dialog->update([
            'block' => $user->id
        ]);
    }

    public function getDialogs(): Paginator
    {
        $user = user();

        $user->markAsReadNotifications([NewMatches::class]);
        return Dialog::withTrashed()
            ->with([
                'lastMessage',
                'lastMessage.attachments',
                'lastMessage.attachments.gift',
                'lastMessage.attachments.property',
                'lastMessage.sender'
            ])
            ->where(function ($builder) use ($user) {
                $builder->where([
                    ['user_id_sender', '=', $user->id],
                    ['user_id_recipient', '!=', $user->id],
                ]);
                $builder->where(function ($builder) use ($user) {
                    $builder->where('deleted_by_user_id', '!=', $user->id);
                    $builder->orWhere('deleted_by_user_id', '=', null);
                });
            })
            ->orWhere(function ($builder) use ($user) {
                $builder->where([
                    ['user_id_recipient', '=', $user->id],
                    ['user_id_sender', '!=', $user->id],
                ]);
                $builder->where(function ($builder) use ($user) {
                    $builder->where('deleted_by_user_id', '!=', $user->id);
                    $builder->orWhere('deleted_by_user_id', '=', null);
                });
            })
            ->distinct()
            ->withCount(['messages as latest_message' => function ($query) {
                $query->select(DB::raw('max(created_at)'));
            }])
            ->orderByDesc('latest_message')
            ->simplePaginate(self::PER_PAGE);
    }
}
