<?php

declare(strict_types=1);

namespace App\Services\Dialog;

use App\Constants\Events;
use App\Events\DialogUpdated;
use App\Events\MessageCreated;
use App\Http\Requests\MessageRequest;
use App\Models\Action;
use App\Models\Dialog;
use App\Models\Image;
use App\Models\Message;
use App\Models\MessageAttachment;
use App\Enums\MessageAttachmentTypes;
use App\Models\User;
use App\Models\UserGift;
use App\Models\UserProperty;
use App\Notifications\NewMessage;
use App\Services\Dialog\Message\Photo;
use App\Services\Dialog\Message\Video;
use App\Services\FileUploader\ImageLoader;
use App\Services\FileUploader\VideoLoader;
use App\Services\PostHog\PosthogService;
use Illuminate\Pagination\CursorPaginator;
use Illuminate\Support\Facades\Storage;

class MessageService
{
    private const PER_PAGE = 15;
    public function __construct(
        private readonly ImageLoader $imageLoader,
        private readonly VideoLoader $videoLoader,
        private readonly PosthogService $postHog
    ) {}

    public function send(Dialog $dialog, MessageRequest $request): Message
    {
        $currentUser = user();
        $messageText = $request->get('text');
        $recipientId = $currentUser->id === $dialog->user_id_sender
            ? $dialog->user_id_recipient
            : $dialog->user_id_sender;

        /** @var Message $message */
        $message = Message::create([
            'dialog_id' => $dialog->id,
            'user_id_sender' => $currentUser->id,
            'user_id_recipient' => $recipientId,
            'message' => $messageText
        ]);

        $this->uploadImages($message->id, $request->file('images') ?? []);
        $this->uploadVideos($message->id, $request->file('videos') ?? []);

        $message->load('recipient');
        $message->recipient->createNotification(new NewMessage($message));
        MessageCreated::dispatch($message);
        DialogUpdated::dispatch($dialog);

        $this->postHog->logEvent(
            Events::MESSAGE_RECEIVED,
            ['user_id' => $currentUser->id],
            $recipientId
        );

        if (!$dialog->is_initiated) {
            $dialog->is_initiated = true;
            $dialog->save();
        } else {
            $this->restoreDialog($dialog) ?? $dialog->touch();
        }

        return $message;
    }

    public function uploadImages(int $messageId, array $images = []): void
    {
        foreach ($images as $image) {
            $photo = new Photo($messageId, $image);
            $this->imageLoader->loading($photo);
            $image = new MessageAttachment([
                'message_id' => $messageId,
                'url' => Storage::url("{$photo->filePath()}/{$photo->fileName()}"),
                'type' => MessageAttachmentTypes::IMAGE
            ]);
            $image->save();
        }
    }

    public function uploadVideos(int $messageId, array $videos = []): void
    {
        foreach ($videos as $video) {
            $videoFile = new Video($messageId, $video);
            $this->videoLoader->loading($videoFile);
            $video = new MessageAttachment([
                'message_id' => $messageId,
                'url' => Storage::url("{$videoFile->filePath()}/{$videoFile->fileName()}"),
                'type' => MessageAttachmentTypes::VIDEO
            ]);
            $video->save();
        }
    }

    private function restoreDialog(Dialog $dialog): bool
    {
        if ($dialog->deleted_at !== null) {
            $dialog->deleted_by = null;
            $dialog->restore();
            return true;
        }

        return false;
    }

    public function getPaginate(Dialog $dialog, ?string $cursor): CursorPaginator
    {
        user()->markAsReadNotifications([NewMessage::class], $dialog->id);

        // updates new messages count on dialog
        DialogUpdated::dispatch($dialog);
        return $dialog
            ->messages()
            ->with([
                'attachments',
                'attachments.gift',
                'attachments.property',
                'sender'
            ])
            ->orderByDesc('created_at')
            ->cursorPaginate(self::PER_PAGE, ['*'], 'historyMessage', $cursor);
    }

    public function sendGift(Dialog $dialog, UserGift $userGift): MessageAttachment
    {
        $user = user();
        $message = $this->createMessageWithGift($dialog, $userGift, $user);
        MessageCreated::dispatch($message);
        return $message->attachments->first();
    }

    public function createMessageWithGift(Dialog $dialog, UserGift $userGift, User $sender): Message
    {
        /** @var Message $message */
        $message = $dialog->messages()->create([
            'user_id_sender' => $sender->id,
            'user_id_recipient' => $sender->id === $dialog->user_id_sender ? $dialog->user_id_recipient
                : $dialog->user_id_sender,
            'message' => "<b>{$userGift->gift->name}</b>",
            'created_at' => $userGift->created_at,
            'updated_at' => $userGift->updated_at
        ]);
        $message->attachments()->save(new MessageAttachment([
            'url' => $userGift->gift->icon_url,
            'type' => MessageAttachmentTypes::GIFT
        ]));
        $message->refresh();
        return $message;
    }

    /**
     * Create message with compliment
     *
     * @param \App\Models\Dialog $dialog
     * @param \App\Models\Action $action
     * @param boolean $isGenerated
     * @return \App\Models\Message
     */
    public function createMessageWithCompliment(Dialog $dialog, Action $action, bool $isGenerated = false): Message
    {
        $complimentUrl = null;
        $property_id = null;
        switch ($action->compliment?->actionable_type) {
            // should render image based on the url
            case Image::class:
                $complimentUrl = $action->compliment->actionable->publicUrl;
                $attachmentType = MessageAttachmentTypes::IMAGE_COMPLIMENT;
                break;
            // should render property based on the property_id
            case UserProperty::class:
                $property_id = $action->compliment->actionable->id;
                $attachmentType = MessageAttachmentTypes::PROPERTY_COMPLIMENT;
                break;
            // should render user description on the frontend
            case User::class:
                $attachmentType = MessageAttachmentTypes::USER_COMPLIMENT;
                break;
            default:
                $complimentUrl = $action->recipient->avatar;
                $attachmentType = MessageAttachmentTypes::IMAGE_COMPLIMENT;
                break;
        }
        /** @var Message $message */
        $message = $dialog->messages()->create([
            'user_id_sender' => $action->sender->id,
            'user_id_recipient' => $action->recipient->id,
            'message' => $action->compliment?->comment ?? "",
            'is_generated' => $isGenerated,
            'created_at' => $action->created_at,
            'updated_at' => $action->updated_at
        ]);
        $message->attachments()->save(new MessageAttachment([
            'url' => $complimentUrl,
            'type' => $attachmentType,
            'property_id' => $property_id
        ]));
        $message->refresh();
        return $message;
    }
}
