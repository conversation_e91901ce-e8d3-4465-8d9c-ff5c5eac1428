<?php

namespace App\Services\PostHog;

use PostHog\PostHog;
use Exception;
use Illuminate\Support\Facades\Log;

class PosthogService
{
    /**
     * Track an event with properties.
     *
     * @param string $event
     * @param array $properties
     * @return array
     */
    public function logEvent(string $event, array $properties = [], int|null $userId = null): array
    {
        try {
            PostHog::init(config('posthog.api_key'));
            if (!$this->isValidEvent($event)) {
                return ["error" => "Invalid event"];
            }

            $distinctId = $userId ?: user()?->id;
            $distinctIdInt = (int)$distinctId;

            PostHog::capture([
                'distinctId' => $distinctIdInt,
                'event' => $event,
                'properties' => $properties,
            ]);
            return ["success" => true];
        } catch (Exception $e) {
            Log::error("Posthog::capture failed with => ", [$e->getMessage()]);
            return ["error" => $e->getMessage()];
        }
    }

    /**
     * Capture an exception.
     *
     * @param Exception $error
     * @return void
     * @throws Exception
     */
    public function captureException(Exception $error): void
    {
        PostHog::capture([
            'event' => 'exception',
            'properties' => ['error_message' => $error->getMessage()],
        ]);
    }

    /**
     * Validates the event name.
     *
     * @param string $event
     * @return bool
     */
    protected function isValidEvent(string $event): bool
    {
        return !empty($event);
    }
}
