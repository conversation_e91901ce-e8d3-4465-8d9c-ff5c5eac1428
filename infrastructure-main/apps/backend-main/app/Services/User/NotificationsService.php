<?php

declare(strict_types=1);

namespace App\Services\User;

use App\Models\User;
use App\Notifications\NewLike;
use App\Notifications\NewMatches;
use App\Notifications\NewMessage;

class NotificationsService
{
    public function __construct(private readonly User $user)
    {
    }

    public function countNewLikes(): int
    {
        return $this->user->unreadNotifications()->where('type', NewLike::class)->count();
    }

    public function countNewMessages(): int
    {
        return $this->user->unreadNotifications()->where('type', NewMessage::class)->count();
    }

    public function countNewMatches(): int
    {
        return $this->user->unreadNotifications()->where('type', NewMatches::class)->count();
    }
}
