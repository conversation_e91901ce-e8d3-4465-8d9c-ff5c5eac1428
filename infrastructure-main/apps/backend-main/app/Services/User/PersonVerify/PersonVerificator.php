<?php

declare(strict_types=1);

namespace App\Services\User\PersonVerify;

use App\Constants\Events;
use App\Enums\Profile\MatchModifier;
use App\Jobs\SendSlackNewVerificationJob;
use App\Enums\Profile\Verify\VerificationMethod;
use App\Enums\Profile\Verify\VerificationStatus;
use App\Events\UserMatchModifiersChanged;
use App\Jobs\SendSlackRevokedVerificationJob;
use App\Models\User;
use App\Models\VerificationRequest;
use App\Notifications\VerificationStatusUpdate;
use App\Services\FileUploader\ImageLoader;
use App\Services\PostHog\PosthogService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class PersonVerificator
{
    public function __construct(private readonly PosthogService $postHog) {}

    public function requestVerification(
        User $user,
        UploadedFile $photo1,
        UploadedFile $photo2
    ): void {
        $this->savePhotos($user, $photo1, $photo2);
        (new VerificationRequest([
            "user_id" => $user->id,
            "method" => VerificationMethod::USING_ID_AND_SELFIE->value,
            "status" => VerificationStatus::PENDING->value,
        ]))->save();
        $record = $user
            ->verificationRequests()
            ->orderByDesc("created_at")
            ->first();
        $this->sendSlackNewVerificationMessage($user, $record->id);
    }

    public function approveVerification(VerificationRequest $record): void
    {
        $record->update(["status" => VerificationStatus::APPROVED->value]);
        $record->user->createNotification(
            new VerificationStatusUpdate(VerificationStatus::APPROVED)
        );
        $this->postHog->logEvent(
            Events::VERIFICATION_APPROVED,
            [],
            $record->user->id
        );
        UserMatchModifiersChanged::dispatch(
            $record->user,
            MatchModifier::VERIFICATION->value
        );
    }

    public function rejectVerification(VerificationRequest $record): void
    {
        $record->update(["status" => VerificationStatus::REJECTED->value]);
        $record->user->createNotification(
            new VerificationStatusUpdate(VerificationStatus::REJECTED)
        );
        $this->postHog->logEvent(
            Events::VERIFICATION_REJECTED,
            [],
            $record->user->id
        );
        Storage::deleteDirectory(
            OriginalPhoto::FOLDER_NAME . "/" . $record->user->id
        );
    }

    public function revokeVerificationIfApproved(User $user): void
    {
        $record = $user
            ->verificationRequests()
            ->orderByDesc("created_at")
            ->first();
        if (
            $record &&
            $record->status === VerificationStatus::APPROVED->value
        ) {
            $record->update(["status" => VerificationStatus::REVOKED->value]);
            UserMatchModifiersChanged::dispatch(
                $user,
                MatchModifier::VERIFICATION->value
            );
            $this->postHog->logEvent(
                Events::VERIFICATION_REVOKED,
                [],
                $user->id
            );
            $this->sendSlackRevokedVerificationMessage($user, $record->id);
        }
    }

    private function savePhotos(
        User $user,
        UploadedFile $photo1,
        UploadedFile $photo2
    ): void {
        $imageLoader = new ImageLoader();

        $photoRule1 = new VerifyPhoto($user, $photo1, 0);
        $imageLoader->loading($photoRule1);

        $photoRule2 = new VerifyPhoto($user, $photo2, 1);
        $imageLoader->loading($photoRule2);
    }

    private function sendSlackNewVerificationMessage(
        User $user,
        int $recordId
    ): void {
        SendSlackNewVerificationJob::dispatch(
            $user->name,
            $recordId
        );
    }

    private function sendSlackRevokedVerificationMessage(
        User $user,
        int $recordId
    ): void {
        SendSlackRevokedVerificationJob::dispatch(
            $user->name,
            $recordId
        );
    }
}
