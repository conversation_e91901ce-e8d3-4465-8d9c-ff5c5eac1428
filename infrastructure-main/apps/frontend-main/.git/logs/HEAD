0000000000000000000000000000000000000000 b78de81b81635291e089bfbc9a78084ef6e7e1d6 <PERSON> <<EMAIL>> 1742394692 +0000	clone: from github.com:Sababuu/frontend-main.git
b78de81b81635291e089bfbc9a78084ef6e7e1d6 a4afff8fff5244199ac58b33b7a0241781b344b1 <PERSON> <<EMAIL>> 1742420032 +0000	checkout: moving from prod to stage
a4afff8fff5244199ac58b33b7a0241781b344b1 a4afff8fff5244199ac58b33b7a0241781b344b1 <PERSON> <<EMAIL>> 1742420060 +0000	checkout: moving from stage to jakob/sab-753-fix-github-pr-descriptionlinear-comment-image-urls
a4afff8fff5244199ac58b33b7a0241781b344b1 13e146588237a26490797f8b4a12bd4effb8a453 <PERSON> <<EMAIL>> 1742420061 +0000	commit: Added repo context to "What did you do"
13e146588237a26490797f8b4a12bd4effb8a453 45bd7d76a6688a560adaa50f14fa6ac2ef3897f7 Jakob Barnwell <<EMAIL>> 1742420072 +0000	commit: Fix Linear comment image URLs
45bd7d76a6688a560adaa50f14fa6ac2ef3897f7 a4afff8fff5244199ac58b33b7a0241781b344b1 Jakob Barnwell <<EMAIL>> 1743071082 +0100	checkout: moving from jakob/sab-753-fix-github-pr-descriptionlinear-comment-image-urls to stage
a4afff8fff5244199ac58b33b7a0241781b344b1 0031eda1ac263131106c6aa03570f2d80d2b393b Jakob Barnwell <<EMAIL>> 1743071085 +0100	pull --ff --recurse-submodules --progress origin: Fast-forward
0031eda1ac263131106c6aa03570f2d80d2b393b 2e04b03dba40e9c8ec26dfb66b73c2b0571b612d Jakob Barnwell <<EMAIL>> 1743071724 +0100	commit: Fix to Org secrets for PR workflow
2e04b03dba40e9c8ec26dfb66b73c2b0571b612d 0c841de99f52ca95db4d3d2d488aa94687d44d3f Jakob Barnwell <<EMAIL>> 1743426037 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
0c841de99f52ca95db4d3d2d488aa94687d44d3f 0c841de99f52ca95db4d3d2d488aa94687d44d3f Jakob Barnwell <<EMAIL>> 1743436692 +0200	checkout: moving from stage to jakob/sab-761-fix-meta-data-tags-for-social-media-linking
0c841de99f52ca95db4d3d2d488aa94687d44d3f 93dcfce44392cec26e2811493b3be3595578f156 Jakob Barnwell <<EMAIL>> 1743436694 +0200	commit: Update to index.html for og meta tags
93dcfce44392cec26e2811493b3be3595578f156 5b360f0e2934643cc58a04bff04246b332ca1599 Jakob Barnwell <<EMAIL>> 1743439683 +0200	checkout: moving from jakob/sab-761-fix-meta-data-tags-for-social-media-linking to stage
5b360f0e2934643cc58a04bff04246b332ca1599 759608ae87122cc79f6ed1b4d866deff96eb1e6c Jakob Barnwell <<EMAIL>> 1743527839 +0200	pull: Fast-forward
759608ae87122cc79f6ed1b4d866deff96eb1e6c be93599ed9c443b6091e33ecae6cb1cdeab2fb59 Jakob Barnwell <<EMAIL>> 1743538964 +0200	checkout: moving from stage to danil/sab-763-hotfixes-after-regress-testing-for-profile-v2
be93599ed9c443b6091e33ecae6cb1cdeab2fb59 56241e78547c8dcc4a6dc7b607b1410290872594 Jakob Barnwell <<EMAIL>> 1743539073 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
56241e78547c8dcc4a6dc7b607b1410290872594 78478366be1b8faf2324bf6be35dede5bb4d9a9c Jakob Barnwell <<EMAIL>> 1743539089 +0200	commit: Copy changes to WhoLikedMe.vue
78478366be1b8faf2324bf6be35dede5bb4d9a9c b06d49e416548e10e976c421d8ffc3309dc01a97 Jakob Barnwell <<EMAIL>> 1743543379 +0200	pull: Fast-forward
b06d49e416548e10e976c421d8ffc3309dc01a97 bb2e190eba0fba222359097066683b89d81afedd Jakob Barnwell <<EMAIL>> 1743600452 +0200	pull: Fast-forward
bb2e190eba0fba222359097066683b89d81afedd a1cf6bd0054a58086bf5484a6d3767ec5b129269 Jakob Barnwell <<EMAIL>> 1743602381 +0200	checkout: moving from danil/sab-763-hotfixes-after-regress-testing-for-profile-v2 to stage
a1cf6bd0054a58086bf5484a6d3767ec5b129269 a1cf6bd0054a58086bf5484a6d3767ec5b129269 Jakob Barnwell <<EMAIL>> 1743710749 +0200	checkout: moving from stage to jakob/sab-766-opening-gallery-doesnt-open-first-photo
a1cf6bd0054a58086bf5484a6d3767ec5b129269 ceff4aee075bad90cfcc35984b5c847a0a568c18 Jakob Barnwell <<EMAIL>> 1743710764 +0200	commit: Update ProfileGallery.vue to always show first photo
ceff4aee075bad90cfcc35984b5c847a0a568c18 a1cf6bd0054a58086bf5484a6d3767ec5b129269 Jakob Barnwell <<EMAIL>> 1743710989 +0200	checkout: moving from jakob/sab-766-opening-gallery-doesnt-open-first-photo to stage
a1cf6bd0054a58086bf5484a6d3767ec5b129269 ceff4aee075bad90cfcc35984b5c847a0a568c18 Jakob Barnwell <<EMAIL>> 1743711123 +0200	checkout: moving from stage to jakob/sab-766-opening-gallery-doesnt-open-first-photo
ceff4aee075bad90cfcc35984b5c847a0a568c18 a1cf6bd0054a58086bf5484a6d3767ec5b129269 Jakob Barnwell <<EMAIL>> 1743712555 +0200	checkout: moving from jakob/sab-766-opening-gallery-doesnt-open-first-photo to stage
a1cf6bd0054a58086bf5484a6d3767ec5b129269 0da933f568b7a5c0ce9bd95fcfaadcf4498bd1a5 Jakob Barnwell <<EMAIL>> 1743777749 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
0da933f568b7a5c0ce9bd95fcfaadcf4498bd1a5 0da933f568b7a5c0ce9bd95fcfaadcf4498bd1a5 Jakob Barnwell <<EMAIL>> 1743778093 +0200	checkout: moving from stage to jakob/sab-764-fix-the-deployment-slack-notification
0da933f568b7a5c0ce9bd95fcfaadcf4498bd1a5 7f4e92e5a280b344bdfeade1984fd48a94355736 Jakob Barnwell <<EMAIL>> 1743778096 +0200	commit: Update frontend deployment workflows
7f4e92e5a280b344bdfeade1984fd48a94355736 0da933f568b7a5c0ce9bd95fcfaadcf4498bd1a5 Jakob Barnwell <<EMAIL>> 1743780019 +0200	checkout: moving from jakob/sab-764-fix-the-deployment-slack-notification to stage
0da933f568b7a5c0ce9bd95fcfaadcf4498bd1a5 f9a6c1c9ed154914b155b8c6530e6552a1ef4546 Jakob Barnwell <<EMAIL>> 1744040347 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
f9a6c1c9ed154914b155b8c6530e6552a1ef4546 f9a6c1c9ed154914b155b8c6530e6552a1ef4546 Jakob Barnwell <<EMAIL>> 1744042075 +0200	checkout: moving from stage to jakob/sab-780-update-survey-fetch-logic-and-rating-surveys
f9a6c1c9ed154914b155b8c6530e6552a1ef4546 246ffa6f6f8b516c8930dfea2a6ee6cdf4c3ce19 Jakob Barnwell <<EMAIL>> 1744042099 +0200	commit: Updates to survey fetch and RATING survey type
246ffa6f6f8b516c8930dfea2a6ee6cdf4c3ce19 f9a6c1c9ed154914b155b8c6530e6552a1ef4546 Jakob Barnwell <<EMAIL>> 1744042521 +0200	checkout: moving from jakob/sab-780-update-survey-fetch-logic-and-rating-surveys to stage
f9a6c1c9ed154914b155b8c6530e6552a1ef4546 a9dd5934eb0771dbe7fcf0d3f2eee1fb8de2021b Jakob Barnwell <<EMAIL>> 1744042570 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
a9dd5934eb0771dbe7fcf0d3f2eee1fb8de2021b 3c918864eecfada434974d2dd7041706e058bbab Jakob Barnwell <<EMAIL>> 1744047720 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
3c918864eecfada434974d2dd7041706e058bbab 3c918864eecfada434974d2dd7041706e058bbab Jakob Barnwell <<EMAIL>> 1744122092 +0200	checkout: moving from stage to jakob-survey-tests
3c918864eecfada434974d2dd7041706e058bbab 3c918864eecfada434974d2dd7041706e058bbab Jakob Barnwell <<EMAIL>> 1744122100 +0200	reset: moving to HEAD
3c918864eecfada434974d2dd7041706e058bbab 3c918864eecfada434974d2dd7041706e058bbab Jakob Barnwell <<EMAIL>> 1744122100 +0200	checkout: moving from jakob-survey-tests to stage
3c918864eecfada434974d2dd7041706e058bbab 3c918864eecfada434974d2dd7041706e058bbab Jakob Barnwell <<EMAIL>> 1744122180 +0200	checkout: moving from stage to jakob-survey-tests
3c918864eecfada434974d2dd7041706e058bbab 0000000000000000000000000000000000000000 Jakob Barnwell <<EMAIL>> 1744136980 +0200	Branch: renamed refs/heads/jakob-survey-tests to refs/heads/jakob/sab-784-update-survey-functionality
0000000000000000000000000000000000000000 3c918864eecfada434974d2dd7041706e058bbab Jakob Barnwell <<EMAIL>> 1744136980 +0200	Branch: renamed refs/heads/jakob-survey-tests to refs/heads/jakob/sab-784-update-survey-functionality
3c918864eecfada434974d2dd7041706e058bbab 7b156acf9d4b8274d6014342773c4fcc13209c62 Jakob Barnwell <<EMAIL>> 1744137016 +0200	commit: Frontend updates to PostHog survey functionality
7b156acf9d4b8274d6014342773c4fcc13209c62 3c918864eecfada434974d2dd7041706e058bbab Jakob Barnwell <<EMAIL>> 1744277324 +0200	checkout: moving from jakob/sab-784-update-survey-functionality to stage
3c918864eecfada434974d2dd7041706e058bbab a075321a07a9f7cab9ef9fb9c99f9f7e59d17d9b Jakob Barnwell <<EMAIL>> 1744277327 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
a075321a07a9f7cab9ef9fb9c99f9f7e59d17d9b 4e933d5b435b357afa716fa37b6b2e140e50d2cb Jakob Barnwell <<EMAIL>> 1744284035 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
4e933d5b435b357afa716fa37b6b2e140e50d2cb 7b156acf9d4b8274d6014342773c4fcc13209c62 Jakob Barnwell <<EMAIL>> 1744284053 +0200	checkout: moving from stage to jakob/sab-784-update-survey-functionality
7b156acf9d4b8274d6014342773c4fcc13209c62 eb2fdf3e2a877c720a815676573734e536c660a4 Jakob Barnwell <<EMAIL>> 1744287006 +0200	commit: Adjustments based on review
eb2fdf3e2a877c720a815676573734e536c660a4 4e933d5b435b357afa716fa37b6b2e140e50d2cb Jakob Barnwell <<EMAIL>> 1744311046 +0200	checkout: moving from jakob/sab-784-update-survey-functionality to stage
4e933d5b435b357afa716fa37b6b2e140e50d2cb 19ab4ff724e79aaa385c2eda725e01c278f86306 Jakob Barnwell <<EMAIL>> 1744311049 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
19ab4ff724e79aaa385c2eda725e01c278f86306 19ab4ff724e79aaa385c2eda725e01c278f86306 Jakob Barnwell <<EMAIL>> 1744461615 +0200	checkout: moving from stage to jakob/sab-791-name-length-validation-not-working-properly-on-onboarding
19ab4ff724e79aaa385c2eda725e01c278f86306 b0f068fb1247df7efe846dc0cb8f92bb7b8ae261 Jakob Barnwell <<EMAIL>> 1744461643 +0200	commit: Updates to name length logic
b0f068fb1247df7efe846dc0cb8f92bb7b8ae261 9df51a09405f88a6f8697844c31285cde87bfb17 Jakob Barnwell <<EMAIL>> 1744461661 +0200	commit: Small typo fixes to CSAE policy
9df51a09405f88a6f8697844c31285cde87bfb17 8ab99941b0e967fbda688d3ecc4d3d791c91b7bb Jakob Barnwell <<EMAIL>> 1744462685 +0200	commit: fix to not allow only whitespace in input field
8ab99941b0e967fbda688d3ecc4d3d791c91b7bb 2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 Jakob Barnwell <<EMAIL>> 1744463545 +0200	commit: Ellipsis review updates
2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 19ab4ff724e79aaa385c2eda725e01c278f86306 Jakob Barnwell <<EMAIL>> 1744464701 +0200	checkout: moving from jakob/sab-791-name-length-validation-not-working-properly-on-onboarding to stage
19ab4ff724e79aaa385c2eda725e01c278f86306 45d3df22b94aa478ff54195076a08b0a276cd76f Jakob Barnwell <<EMAIL>> 1744721819 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
45d3df22b94aa478ff54195076a08b0a276cd76f 2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 Jakob Barnwell <<EMAIL>> 1744727091 +0200	checkout: moving from stage to jakob/sab-791-name-length-validation-not-working-properly-on-onboarding
2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 Jakob Barnwell <<EMAIL>> 1744727898 +0200	reset: moving to HEAD
2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 45d3df22b94aa478ff54195076a08b0a276cd76f Jakob Barnwell <<EMAIL>> 1744727898 +0200	checkout: moving from jakob/sab-791-name-length-validation-not-working-properly-on-onboarding to stage
45d3df22b94aa478ff54195076a08b0a276cd76f 2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 Jakob Barnwell <<EMAIL>> 1744727910 +0200	checkout: moving from stage to jakob/sab-791-name-length-validation-not-working-properly-on-onboarding
2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 Jakob Barnwell <<EMAIL>> 1744728991 +0200	reset: moving to HEAD
2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 45d3df22b94aa478ff54195076a08b0a276cd76f Jakob Barnwell <<EMAIL>> 1744728991 +0200	checkout: moving from jakob/sab-791-name-length-validation-not-working-properly-on-onboarding to stage
45d3df22b94aa478ff54195076a08b0a276cd76f 2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 Jakob Barnwell <<EMAIL>> 1744729778 +0200	checkout: moving from stage to jakob/sab-791-name-length-validation-not-working-properly-on-onboarding
2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 Jakob Barnwell <<EMAIL>> 1744731127 +0200	reset: moving to HEAD
2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 45d3df22b94aa478ff54195076a08b0a276cd76f Jakob Barnwell <<EMAIL>> 1744731127 +0200	checkout: moving from jakob/sab-791-name-length-validation-not-working-properly-on-onboarding to stage
45d3df22b94aa478ff54195076a08b0a276cd76f 2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 Jakob Barnwell <<EMAIL>> 1744731256 +0200	checkout: moving from stage to jakob/sab-791-name-length-validation-not-working-properly-on-onboarding
2ccd41742a3ec9aa87a7d7ca215b19f175d9c3d0 2fd13313c8b7d2e37ae0f7bf57802971f4145d38 Jakob Barnwell <<EMAIL>> 1744731544 +0200	commit: addressing requested changes
2fd13313c8b7d2e37ae0f7bf57802971f4145d38 45d3df22b94aa478ff54195076a08b0a276cd76f Jakob Barnwell <<EMAIL>> 1744733814 +0200	checkout: moving from jakob/sab-791-name-length-validation-not-working-properly-on-onboarding to stage
45d3df22b94aa478ff54195076a08b0a276cd76f 45d3df22b94aa478ff54195076a08b0a276cd76f Jakob Barnwell <<EMAIL>> 1744809094 +0200	checkout: moving from stage to jakob/sab-792-split-my-profile-boost-settings-on-own-profile
45d3df22b94aa478ff54195076a08b0a276cd76f 4d059fdb31cd972f7cfc04ce9b2b1cfcb7d0a764 Jakob Barnwell <<EMAIL>> 1744809131 +0200	commit: First steps to profile split
4d059fdb31cd972f7cfc04ce9b2b1cfcb7d0a764 f8834eb3516361f4be3b3c8e77f8cc67e2e689bb Jakob Barnwell <<EMAIL>> 1744810974 +0200	commit: Settings tab finished + change to delete_survey_other_reason length
f8834eb3516361f4be3b3c8e77f8cc67e2e689bb 2583d9e13b4a892ae8c97a39925de7626c95ce48 Jakob Barnwell <<EMAIL>> 1744916139 +0200	commit: New Likes page
2583d9e13b4a892ae8c97a39925de7626c95ce48 fe402fc6a1b219c94922cd4fde1faa0f5e8f9553 Jakob Barnwell <<EMAIL>> 1744916146 +0200	pull --ff --recurse-submodules --progress origin: Merge made by the 'ort' strategy.
fe402fc6a1b219c94922cd4fde1faa0f5e8f9553 56bce8e9ba076a327150f2085f72a931659e2b96 Jakob Barnwell <<EMAIL>> 1744922459 +0200	commit: render gifts a bit better
56bce8e9ba076a327150f2085f72a931659e2b96 4b9909758d19e1bedf3d0fee4e3b0c0c978565cf Jakob Barnwell <<EMAIL>> 1744931482 +0200	commit: new PropertyCard in Discovery feed
4b9909758d19e1bedf3d0fee4e3b0c0c978565cf 3f3ce060873f0f433b0299395c1ac385491cf702 Jakob Barnwell <<EMAIL>> 1744932278 +0200	commit: improvements to Discovery's Flowcard PropertyCard
3f3ce060873f0f433b0299395c1ac385491cf702 643e95925ee1b689eaddc4b15ede12fb7ef10eab Jakob Barnwell <<EMAIL>> 1744967908 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
643e95925ee1b689eaddc4b15ede12fb7ef10eab 67b30e33cb4d61cfb5c1011ad71f06d6991115f8 Jakob Barnwell <<EMAIL>> 1744969427 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
67b30e33cb4d61cfb5c1011ad71f06d6991115f8 f2852b324168ef9b5b2fa96d6aac9b7c4c361cda Jakob Barnwell <<EMAIL>> 1744970388 +0200	commit: wallet and boost as profile tabs
f2852b324168ef9b5b2fa96d6aac9b7c4c361cda fec80ebb5930fb05abc6e4f2ff38bb7ac057dd40 Jakob Barnwell <<EMAIL>> 1745001714 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
fec80ebb5930fb05abc6e4f2ff38bb7ac057dd40 b6a0eb2fd214ad057217ee4cb1798432b3689d1d Jakob Barnwell <<EMAIL>> 1745008985 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
b6a0eb2fd214ad057217ee4cb1798432b3689d1d c7cc7262b224af54b2051f0b4d8f700cc735c7fa Jakob Barnwell <<EMAIL>> 1745012558 +0200	commit: Small change to end of verification flow
c7cc7262b224af54b2051f0b4d8f700cc735c7fa aca2f5b935b92670d4ca91c97b867352a456964b Jakob Barnwell <<EMAIL>> 1745012590 +0200	commit: small copy change on FeedRevealConfirmation
aca2f5b935b92670d4ca91c97b867352a456964b d43d830efed43517bc8280eff20d6eb80f511dbc Jakob Barnwell <<EMAIL>> 1745012603 +0200	commit: add remove button to Bio Rditor
d43d830efed43517bc8280eff20d6eb80f511dbc 44ce98ad1621f1255255ef6e2eeafd2f6e32c8a1 Jakob Barnwell <<EMAIL>> 1745012621 +0200	commit: PropertyCard (flow card) improvements in Discovery
44ce98ad1621f1255255ef6e2eeafd2f6e32c8a1 26e2f1105ec15ceb5b48d7b4cbe4b9cebcf521b4 Jakob Barnwell <<EMAIL>> 1745013838 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
26e2f1105ec15ceb5b48d7b4cbe4b9cebcf521b4 9d11d178219f538a57e0c2d1b4346372c07eca03 Jakob Barnwell <<EMAIL>> 1745016012 +0200	commit: use global properties instead of get onmounted
9d11d178219f538a57e0c2d1b4346372c07eca03 2f88ec4cf9b3ef1063a56a38e2bf352818868a20 Jakob Barnwell <<EMAIL>> 1745016346 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
2f88ec4cf9b3ef1063a56a38e2bf352818868a20 9e4b56c798548bd8b53532a5ca6e35a6578e088b Jakob Barnwell <<EMAIL>> 1745074066 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
9e4b56c798548bd8b53532a5ca6e35a6578e088b 39af58b49ebe775a1fd3c1bab7347096342be79b Jakob Barnwell <<EMAIL>> 1745256238 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
39af58b49ebe775a1fd3c1bab7347096342be79b 3c7f425cd4a5e8c2f241ab9e255a31ae9392d2bb Jakob Barnwell <<EMAIL>> 1745317742 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
3c7f425cd4a5e8c2f241ab9e255a31ae9392d2bb d9860e71f3f824f507712f48bd2b63a8eb40389f Jakob Barnwell <<EMAIL>> 1745326183 +0200	commit: rename PropertyCards>UpdateCards and remove conditional logic from component
d9860e71f3f824f507712f48bd2b63a8eb40389f 136549c580cf1ee353804e974372ff0b03dfb6d3 Jakob Barnwell <<EMAIL>> 1745327066 +0200	commit: fix when removeUpdateCard should be called during verification
136549c580cf1ee353804e974372ff0b03dfb6d3 ae8b361c9850a727860eb106bcb815918fbba7b0 Jakob Barnwell <<EMAIL>> 1745332819 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
ae8b361c9850a727860eb106bcb815918fbba7b0 ca10ba6c9d38580484ad3d35711f1fbcc37b277e Jakob Barnwell <<EMAIL>> 1745338065 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
ca10ba6c9d38580484ad3d35711f1fbcc37b277e 43a93db5831ce9e6d489c088ca285b85f4a1d5e3 Jakob Barnwell <<EMAIL>> 1745344202 +0200	commit: Updates to uploader (Jakob)
43a93db5831ce9e6d489c088ca285b85f4a1d5e3 bb6fe33d739e2a5452ec350b2a71a31aaf03191c Jakob Barnwell <<EMAIL>> 1745344567 +0200	commit: Always show prompt cards in Discovery
bb6fe33d739e2a5452ec350b2a71a31aaf03191c 1b158b558a526d69b829d6e04ac2caf7534d6f9e Jakob Barnwell <<EMAIL>> 1745354762 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
1b158b558a526d69b829d6e04ac2caf7534d6f9e 3a339822004126371892734428240163339cd2a1 Jakob Barnwell <<EMAIL>> 1745362775 +0200	commit: Delete old PropertyCard components (renamed to UpdateCard)
3a339822004126371892734428240163339cd2a1 b91254f0157080c11504bdb18cdbd5d6e4b4c3ab Jakob Barnwell <<EMAIL>> 1745362861 +0200	commit: Fix prompt persistence between tabs
b91254f0157080c11504bdb18cdbd5d6e4b4c3ab e4a00c0e8e299903774571bc1f1f1f32f8bf096a Jakob Barnwell <<EMAIL>> 1745362942 +0200	commit: Fix for updateCard array after action
e4a00c0e8e299903774571bc1f1f1f32f8bf096a 3c8c215665a11f90e7dcfa3d6879a3ccb5c3f14b Jakob Barnwell <<EMAIL>> 1745363038 +0200	commit: Hide Discovery filter button during verification
3c8c215665a11f90e7dcfa3d6879a3ccb5c3f14b bb8812b5fd035fe27aed8735a672cc2aac17a835 Jakob Barnwell <<EMAIL>> 1745363070 +0200	commit: Updates to Prompts UpdateCards
bb8812b5fd035fe27aed8735a672cc2aac17a835 41fdb53d670577f46760cc1a03136c3c4fbf7da4 Jakob Barnwell <<EMAIL>> 1745402644 +0200	commit: Copy changes to TryBoostingHelper
41fdb53d670577f46760cc1a03136c3c4fbf7da4 ed55167a3753dec36b5078cbf827cef486465f34 Jakob Barnwell <<EMAIL>> 1745403172 +0200	commit: Small edits to "Back soon" position for GH users
ed55167a3753dec36b5078cbf827cef486465f34 7f5037b1e1b48e183d48550e12055bd915ea8f09 Jakob Barnwell <<EMAIL>> 1746433563 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
7f5037b1e1b48e183d48550e12055bd915ea8f09 ca5bdd1f72d511064e455bcdf608122b1bfe060d Jakob Barnwell <<EMAIL>> 1746433568 +0200	checkout: moving from jakob/sab-792-split-my-profile-boost-settings-on-own-profile to stage
ca5bdd1f72d511064e455bcdf608122b1bfe060d 46483b1f4208bd3fd75b3c674fb3d92da123d029 Jakob Barnwell <<EMAIL>> 1746695438 +0200	pull: Fast-forward
46483b1f4208bd3fd75b3c674fb3d92da123d029 30e854da87270d11bf9f2d2798a204998b8be499 Jakob Barnwell <<EMAIL>> 1747124742 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
30e854da87270d11bf9f2d2798a204998b8be499 538c07aa74a012a1785d3b41de4c261660265abe Jakob Barnwell <<EMAIL>> 1747822135 +0100	pull --ff --recurse-submodules --progress origin: Fast-forward
538c07aa74a012a1785d3b41de4c261660265abe 93fa79733fa27949797918078800a9481ddccf41 Jakob Barnwell <<EMAIL>> 1748421777 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
93fa79733fa27949797918078800a9481ddccf41 819e6abeef7ad94c28f82dac8d7379ae7462c445 Jakob Barnwell <<EMAIL>> 1748455502 +0200	pull --ff --recurse-submodules --progress origin: Fast-forward
