import { restApi } from "@/api/index";

/**
 * Fetches profile information for a specific user by their ID
 * @param {string|number} userId - The ID of the user whose profile to fetch
 * @returns {Promise} Promise representing the API response with UserResource data
 */
export const guestProfile = (userId) => {
  return restApi.get(`/user/${userId}`);
};

/**
 * Fetches the authenticated user's own profile information
 * @returns {Promise} Promise representing the API response with OwnUserResource data including gallery and communities
 */
export const ownProfile = () => {
  return restApi.get("/user");
};

/**
 * Updates the user's profile information
 * @param {{
 *   name?: string,
 *   birthday?: string,
 *   location?: string,
 *   description?: string,
 *   avatar?: string,
 *   phone?: number,
 *   latitude?: number,
 *   longitude?: number
 * }} data - The user profile data to update where:
 *   - name: User's name (1-25 characters)
 *   - birthday: User's birthday in YYYY-MM-DD format (must be between min and max age)
 *   - location: User's city (must be valid for user's country)
 *   - description: User's description (1-200 characters)
 *   - avatar: URL or identifier for user's avatar image
 *   - phone: User's phone number (must be unique and valid for country)
 *   - latitude: Geographic latitude coordinate
 *   - longitude: Geographic longitude coordinate
 * @returns {Promise} Promise representing the API response with updated OwnUserResource data
 */
export const updateProfile = (data) => {
  return restApi.post("/user/update", data);
};

/**
 * Fetches verification rules and requirements for person verification
 * @returns {Promise} Promise representing the API response with verification rules
 */
export const profileVerificationRuleRequest = () => {
  return restApi.get("/user/person-verification");
};

/**
 * Submits a verification request to the server
 * @param {{selfie_photo1: Object, selfie_photo2: Object}} data - An object with two properties:
 *   - selfie_photo1: Binary data of the first verification selfie image
 *   - selfie_photo2: Binary data of the second verification selfie image
 * @returns {Promise} Promise representing the API response
 */
export const profileVerificationRequest = (data) => {
  return restApi.post("/user/person-verification", data);
};
/**
 * Deletes the current user's profile
 * @returns {Promise} Promise representing the API response
 */
export const removeProfile = () => {
  return restApi.delete(`user`);
};

/**
 * Subscribes the user to push notifications
 * @param {Object} data - Subscription data for push notifications
 * @param {string} data.subscription_id - The OneSignal subscription ID
 * @returns {Promise} Promise representing the API response
 */
export const sendPushNotificationsSubscription = (data) => {
  return restApi.post("user/notifications/subscribe", data);
};

export const fetchUserData = () => {
  return restApi.get("/user");
};

export const fetchNotificationIndicators = () => {
  return restApi.get("/user/notifications");
};

export const heartBeat = () => {
  return restApi.get("user/heart-beat");
};

export const sendPhoneVerificationCode = (phone) => {
  return restApi.post("user/send-phone-verification", phone);
};

export const updatePhone = (phone, code) => {
  return restApi.post("user/update-phone", { phone, code });
};

export const updateOneSignal = (subscription_id) => {
  return restApi.post("user/one-signal", { subscription_id });
};

export const deleteOneSignal = () => {
  return restApi.delete("user/one-signal");
};
