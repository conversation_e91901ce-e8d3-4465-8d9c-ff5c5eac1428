<template>
  <div v-if="showMenu" class="menu">
    <div class="menu__container">
      <div
        class="menu__item"
        :class="{ menu__item__active: activeMenuItem == 'discovery' }"
        @click="setActiveMenuItem('discovery')"
      >
        <DiscoverIcon />
        <span class="menu-text">Discover</span>
      </div>
      <div
        id="btn-matches"
        class="menu__item"
        :class="{
          menu__item__active: activeMenuItem == 'whoLikedMe',
          menu__item__notify:
            notifyStore.getNotifications.new_likes_notifications > 0 &&
            activeMenuItem != 'whoLikedMe',
          // menu__item__notify: true,
        }"
        @click="setActiveMenuItem('whoLikedMe')"
      >
        <HeartIcon />
        <span>Liked You</span>
      </div>
      <div
        id="btn-onlinenow"
        class="menu__item"
        :class="{
          menu__item__active: activeMenuItem == 'online',
          'menu__item__notify green':
            userPresenceStore.onlineUsers.length > 1 &&
            activeMenuItem != 'online',
        }"
        @click="setActiveMenuItem('online')"
      >
        <OnlineIcon />
        <span>Online</span>
      </div>
      <div
        id="btn-dialogs"
        class="menu__item"
        :class="{
          menu__item__active: activeMenuItem == 'dialogs',
          menu__item__notify:
            (notifyStore.getNotifications.new_messages_notifications > 0 ||
              notifyStore.getNotifications.new_matches_notifications > 0) &&
            activeMenuItem != 'dialogs',
        }"
        @click="setActiveMenuItem('dialogs')"
      >
        <ChatIcon />
        <span>Chats</span>
      </div>
      <div
        id="btn-profile"
        class="menu__item"
        :class="{ menu__item__active: activeMenuItem == 'profile' }"
        @click="setActiveMenuItem('profile')"
      >
        <ProfileIcon />
        <span>Profile</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useNotifyStore } from "@/stores/notify";
import { useUserPresenceStore } from "@/stores/userPresence";

import DiscoverIcon from "@/assets/icons/navigation/discovery.svg";
import HeartIcon from "@/assets/icons/navigation/heart-new.svg";
import ChatIcon from "@/assets/icons/navigation/chat.svg";
import OnlineIcon from "@/assets/icons/navigation/online-now-new.svg";
import ProfileIcon from "@/assets/icons/navigation/profile.svg";
import { logEvent } from "@/utils/tracking.js";

const route = useRoute();
const router = useRouter();
const userPresenceStore = useUserPresenceStore();

const setActiveMenuItem = (menuItem) => {
  logEvent(`navigation_menu_${menuItem}_click`);
  switch (menuItem) {
    case "discovery":
      router.push({ name: "discovery" });
      break;
    case "whoLikedMe":
      router.push({ name: "wholikedme" });
      break;
    case "dialogs":
      router.push({ name: "dialogs" });
      break;
    case "profile":
      router.push({ name: "profile" });
      break;
    case "online":
      router.push({ name: "online" });
      break;
    default:
      return null;
  }
};

const activeMenuItem = computed(() => {
  switch (route.name) {
    case "discovery":
      return "discovery";
    case "wholikedme":
    case "wholikedme2":
      return "whoLikedMe";
    case "dialogs":
    case "dialog":
      return "dialogs";
    case "profile":
    case "profile.boosts":
    case "profile.settings":
    case "profile.wallet":
      return "profile";
    case "online":
      return "online";
    default:
      return undefined;
  }
});

const showMenu = computed(() => {
  return (
    route.name == "discovery" ||
    route.name == "wholikedme" ||
    route.name == "wholikedme2" ||
    route.name == "dialogs" ||
    route.name == "dialog" ||
    route.name.startsWith("profile") ||
    route.name == "profile.own.v2" ||
    route.name == "online" ||
    route.name == "profile.guest" ||
    route.name == "profile.online"
  );
});

const notifyStore = useNotifyStore();
</script>

<style lang="scss" scoped>
.menu {
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  border-top: 1px solid $navigation-border-color;
  height: 60px;
  width: 100%;
}

.menu__container {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: fit-content;
  height: 100%;
  flex: 1;
}

.menu__item {
  background: none;
  height: calc(100% + 2px);
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  row-gap: 2px;
  color: #8e8f99;
  position: relative;

  & > svg {
    height: 22px;
    width: 22px;
  }

  & > span {
    font-size: 10px;
  }

  &__active {
    color: $primary-color;
  }

  &__notify::before {
    content: "";
    position: absolute;
    height: 8px;
    width: 8px;
    z-index: 1;
    border-radius: 50%;
    background-color: $primary-color;
    top: 25px;
    right: 27px;
  }

  &__notify.green::before {
    background-color: #51dd2f;
  }
}
</style>
