<template>
  <div class="profile-card-wrapper">
    <div
      class="profile__card"
      :class="{
        is_cld_image: IS_CLOUDINARY_URL.test(profile.avatar),
        on_discovery: isProfileFromDiscovery,
      }"
    >
      <template
        v-for="section in renderOrder"
        :key="section.id || section.type"
      >
        <div
          v-if="section.type === 'avatar'"
          class="profile__card__avatar"
          :class="{
            is_boosted: profile.is_boosted && isProfileFromDiscovery,
          }"
        >
          <!-- This is a hack because of our current profile card header behaviour -->
          <slot name="header"></slot>
          <div class="profile__avatar__photo__wrapper">
            <ProfileActionable
              :actionable-id="profile.avatar_id"
              :actionable-type="ACTIONABLE_TYPES.IMAGE"
              actionable-name="photo"
              :user="profile"
              :can-do-actions="canDoActionsOnProfile"
              @action="$emit('action')"
            >
              <template #gift>
                <ButtonGift :profile="profile" :source="'profile-action'" />
              </template>
              <BoostedProfileHeader
                v-if="profile.is_boosted && isProfileFromDiscovery"
                class="boosted_profile_header"
                :boost-package="boostPackage"
              />
              <SababuuImage
                :source="profile.avatar"
                img-class="profile__avatar__photo"
              />
            </ProfileActionable>
          </div>
          <div
            class="profile__avatar__item"
            :class="{
              is_boosted: profile.is_boosted && isProfileFromDiscovery,
            }"
          >
            <div class="profile__avatar__item__content_name">
              <div
                v-if="profile.person_verification && profile.name.length > 17"
                class="profile__avatar__item__content_name__verified"
              >
                <CheckBadgeIcon /> Verified
              </div>
              <div class="profile__avatar__item__content_name__wrapper">
                <span>{{ profile.name }}</span>
                <div
                  v-if="
                    profile.person_verification && profile.name.length <= 17
                  "
                  class="profile__avatar__item__content_name__verified"
                >
                  <CheckBadgeIcon /> Verified
                </div>
              </div>
            </div>
            <div class="profile__avatar__item__content_location">
              <CakeIcon /> {{ profile.age }} years old
            </div>
            <div class="profile__avatar__item__content_location">
              <LocationIcon /> {{ profile.distance }} km away
            </div>
            <div class="profile__avatar__item__content_last_seen">
              <ProfileLastSeen :profile="profile" />
            </div>
          </div>
        </div>
        <!-- Free reveal notification -->
        <div
          v-else-if="section.type === 'free-reveal'"
          class="profile_card_item free-reveal-notification"
        >
          <div class="free-reveal-notification__content">
            <div class="free-reveal-notification__text">
              <strong>This reveal was free!🎉</strong>
              <span
                >You were not charged 1 credit because you had already disliked
                this user in the past.</span
              >
            </div>
          </div>
        </div>
        <ProfileActionable
          v-else-if="section.type === 'about' && profile.description"
          class="profile_card_item"
          :class="{ 'not-actionable': !canDoActionsOnProfile }"
          :actionable-id="profile.id"
          :actionable-type="ACTIONABLE_TYPES.USER"
          actionable-name="description"
          :user="profile"
          :can-do-actions="canDoActionsOnProfile"
          @action="$emit('action')"
        >
          <ProfilePropertyTextCard
            :property="{ name: 'About', value: profile.description }"
          />
        </ProfileActionable>

        <ProfileBioAndInterests
          v-else-if="section.type === 'bio-interests' && hasBioOrInterests"
          class="profile_card_item"
          :bio-properties="bioProperties"
          :interests-properties="interestsProperties"
        />

        <div
          v-else-if="
            section.type === 'instachat-button' &&
            showInstachat &&
            !isProfileFromOnlineTab
          "
          class="profile__info__item"
        >
          <ButtonInstachat :profile="profile" />
        </div>

        <div
          v-else-if="
            section.type === 'online-chat-button' && isProfileFromOnlineTab
          "
          class="profile__info__chat"
        >
          <OnlineChatButton :profile="profile" :style="'large'" />
        </div>

        <div
          v-else-if="section.type === 'image' && section.data"
          class="profile__gallery__item"
        >
          <div class="profile__gallery__photo__wrapper">
            <ProfileActionable
              :actionable-id="section.data.id"
              :actionable-type="ACTIONABLE_TYPES.IMAGE"
              actionable-name="photo"
              :user="profile"
              :can-do-actions="canDoActionsOnProfile"
              @action="$emit('action')"
            >
              <SababuuImage
                :source="section.data.path"
                img-class="profile__gallery__photo"
              />
            </ProfileActionable>
          </div>
        </div>

        <ProfileActionable
          v-else-if="section.type === 'sprinkle' && section.data"
          class="profile_card_item"
          :class="{ 'not-actionable': !canDoActionsOnProfile }"
          :actionable-id="section.data.id"
          :actionable-type="ACTIONABLE_TYPES.USER_PROPERTY"
          :actionable-name="
            section.data.validation_type === 'text' ? 'answer' : 'photo'
          "
          :user="profile"
          :can-do-actions="canDoActionsOnProfile"
          @action="$emit('action')"
        >
          <ProfilePropertySprinkleCard :sprinkle-property="section.data" />
        </ProfileActionable>
      </template>
      <div class="profile__card__end">
        <span
          v-if="canDoActionsOnProfile"
          class="profile__card__end__instructions"
        >
          <LikeIcon /> Like or <DislikeIcon /> Pass to see another profile.
        </span>
        <span class="profile__card__end__report" @click="reportApprove">
          <NoIcon /> Hide and report this user
        </span>
      </div>
    </div>

    <ButtonDislike
      v-if="
        profile.actionable && !isProfileFromOnlineTab && !profile.is_boosted
      "
      :user="profile"
      @action="$emit('action', true)"
    />
  </div>
</template>

<script setup>
import { useRoute } from "vue-router";
import { computed, onMounted } from "vue";
import bus from "@/bus";

import SababuuImage from "@/components/SababuuImage.vue";
import ButtonInstachat from "@/components/buttons/ButtonInstachat.vue";
import ButtonGift from "@/components/buttons/ButtonGift.vue";
import ButtonDislike from "@/components/buttons/ButtonDislike.vue";
import OnlineChatButton from "@/components/buttons/OnlineChatButton.vue";
import BoostedProfileHeader from "@/components/profile/boost/BoostedProfileHeader.vue";
import ProfilePropertyTextCard from "./profile/ProfilePropertyTextCard.vue";
import ProfilePropertySprinkleCard from "./profile/ProfilePropertySprinkleCard.vue";
import ProfileBioAndInterests from "./profile/ProfileBioAndInterests.vue";
import ProfileActionable from "./profile/ProfileActionable.vue";
import ProfileLastSeen from "./profile/ProfileLastSeen.vue";

import LocationIcon from "@/assets/icons/location-icon.svg";
import CakeIcon from "@/assets/icons/cake.svg";
import DislikeIcon from "@/assets/icons/dislike.svg";
import LikeIcon from "@/assets/icons/like.svg";
import NoIcon from "@/assets/icons/no-symbol.svg";
import CheckBadgeIcon from "@/assets/icons/check-badge.svg";

import { IS_CLOUDINARY_URL, IS_DEFAULT_DESCRIPTION } from "@/utils/helpers";

import { logEvent } from "@/utils/tracking.js";
import trackedEvents from "@/utils/trackedEvents.js";

import { ACTIONABLE_TYPES } from "@/constants";

defineEmits(["action"]);

const route = useRoute();

const props = defineProps({
  profile: {
    type: Object,
    required: true,
  },
  showInstachat: {
    type: Boolean,
    default: true,
  },
});

const isProfileFromOnlineTab = computed(() => {
  return route.name === "profile.online";
});

const isProfileFromDiscovery = computed(() => {
  return route.name === "discovery";
});

const canDoActionsOnProfile = computed(() => {
  return (
    props.profile.actionable &&
    !props.profile.is_boosted &&
    !isProfileFromOnlineTab.value
  );
});

const boostPackage = computed(() => {
  const boosts = props.profile?.profile_boosts;
  if (!boosts || boosts.length < 1) return null;

  return boosts[0].boost_package;
});

const bioProperties = computed(() =>
  (props.profile.properties ?? []).filter((p) => p.category_name === "Bio")
);

const interestsProperties = computed(() =>
  (props.profile.properties ?? [])
    .filter((p) => p.category_name === "Interests")
    .map((i) => i.value)
);

const promptProperties = computed(() =>
  (props.profile.properties ?? []).filter((p) => p.category_name === "Sprinkle")
);

const hasBioOrInterests = computed(
  () =>
    (bioProperties.value?.length ?? 0) > 0 ||
    (interestsProperties.value?.length ?? 0) > 0
);

const hasDescription = (description = "") =>
  IS_DEFAULT_DESCRIPTION.test(description) === false;

const renderOrder = computed(() => {
  const [firstGalleryImage, ...restGalleryImages] = props.profile.gallery ?? [];
  const sprinkles = promptProperties.value ?? [];
  const maxRenderableItemsLength = Math.max(
    restGalleryImages.length,
    sprinkles.length
  );

  const order = [{ type: "avatar", id: "avatar" }];

  if (props.profile.is_free_reveal) {
    order.push({ type: "free-reveal", id: "free-reveal" });
  }

  if (
    props.showInstachat &&
    !isProfileFromOnlineTab.value &&
    props.profile.is_boosted
  ) {
    order.push({ type: "instachat-button", id: "instachat-btn" });
  }

  if (isProfileFromOnlineTab.value) {
    order.push({ type: "online-chat-button", id: "onlinechat-btn" });
  }

  if (firstGalleryImage) {
    order.push({
      type: "image",
      data: firstGalleryImage,
      id: firstGalleryImage.id,
    });
  }

  if (
    props.showInstachat &&
    !isProfileFromOnlineTab.value &&
    !props.profile.is_boosted
  ) {
    order.push({ type: "instachat-button", id: "instachat-btn" });
  }

  if (hasDescription(props.profile.description)) {
    order.push({ type: "about", id: "about" });
  }

  if (hasBioOrInterests.value) {
    order.push({ type: "bio-interests", id: "bio-interests" });
  }

  for (let i = 0; i < maxRenderableItemsLength; i++) {
    const image = restGalleryImages[i];
    const sprinkle = sprinkles[i];

    if (image) {
      order.push({ type: "image", data: image, id: image.id });
    }

    if (sprinkle) {
      order.push({ type: "sprinkle", data: sprinkle, id: sprinkle.id });
    }
  }

  return order;
});

onMounted(() => {
  if (props.profile.is_boosted) {
    logEvent(trackedEvents.BOOSTED_PROFILE_SEEN, {
      profile_id: props.profile.id,
    });
  }
});

const reportApprove = () => {
  bus.global.emit("modal.chatActions:open", {
    name: "reportAreYouSure",
  });
};
</script>

<style lang="scss" scoped>
.profile-card-wrapper {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.profile__card {
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 1rem;
  flex-grow: 1;
  overflow-y: auto;
  min-height: 0;

  &.on_discovery {
    padding-top: 8px;

    .profile__card__end {
      padding-bottom: 90px;
    }
  }

  .profile_card_item {
    margin-right: 16px;
    margin-left: 16px;

    & > div.property-card {
      margin-bottom: 0 !important;
      padding-bottom: 55px !important;
      box-shadow: $profile-card-item-shadow-sm;
      background-color: white !important;
      border: 1px solid $profile-card-item-border-color;
      border-radius: $app-card-border-radius;

      & ::v-deep(.property-card__image) {
        box-shadow: $profile-card-item-shadow-lg;
        border-radius: $app-card-border-radius;

        & > img {
          border-radius: $app-card-border-radius;
        }
      }
    }

    &.not-actionable > div.property-card {
      padding-bottom: 16px !important;
    }
    .free-reveal-notification__content {
      display: flex;
      place-items: center;
      flex-direction: column;
      padding: 20px;
      border: 1px solid #e94057b8;
      border-radius: 15px;
      .free-reveal-notification__text {
        display: flex;
        flex-direction: column;
        font-size: 0.9rem;
      }
    }
  }

  .profile__card__end {
    margin-top: auto;
    margin-bottom: 0;
  }

  .boosted_profile_header {
    margin-bottom: 0 !important;
    flex-shrink: 0;
  }

  .profile__card__avatar {
    width: 100%;
    position: relative;
    flex-shrink: 0;
    padding: 0px 16px;

    .profile__avatar__photo__wrapper {
      height: 100%;
      overflow: hidden;
      border-radius: $app-card-border-radius;

      & > .actionable-wrapper {
        display: block;
        height: 100%;
        width: 100%;
      }

      & ::v-deep(.profile__avatar__photo) {
        height: 100%;
        width: 100%;
        object-fit: cover;
        object-position: center;
        display: block;
        border-radius: $app-card-border-radius;
      }
    }

    & > .profile__avatar__item {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 16px;
      right: 16px;
      padding: 10px 16px 16px 16px;
      border-radius: $app-card-border-radius;
      background: linear-gradient(
        to top,
        rgba(0, 0, 0, 0.8) 0%,
        rgba(0, 0, 0, 0) 70%
      );

      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      box-sizing: border-box;
      row-gap: 5px;

      .profile__avatar__item__content {
        position: relative;

        &_name {
          color: white;
          font-weight: 900;
          font-size: larger;
          display: flex;
          flex-direction: column;
          gap: 0.375rem;

          &__wrapper {
            align-items: center;
            display: flex;
            gap: 0.375rem;

            & > span {
              max-width: 85%;
            }
          }

          &__verified {
            background-color: $primary-color;
            color: #fff;
            font-weight: 400;
            font-size: 12px;
            display: inline-flex;
            column-gap: 0.375rem;
            align-items: center;
            border-radius: 5px;
            padding: 2px 0px;
            vertical-align: middle;
            width: 80px;
            justify-content: center;

            & > svg {
              height: 16px;
              width: 16px;
            }
          }
        }

        &_location {
          color: white;
          font-weight: 400;
          font-size: small;
          display: flex;
          column-gap: 0.375rem;
          align-items: center;

          & > svg {
            height: 16px;
            width: 16px;
          }
        }

        &_last_seen {
          color: white;
        }
      }
    }

    &.is_boosted {
      & ::v-deep(.profile__avatar__photo) {
        border-top-left-radius: 0px;
        border-top-right-radius: 0px;
      }
    }
  }

  .profile__info__chat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    width: 100%;
    min-height: 60px;
    box-sizing: border-box;
    flex-shrink: 0;
  }

  .profile__info__item {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 5px;
    padding-left: 16px;
    padding-right: 16px;
    box-sizing: border-box;
    flex-shrink: 0;
    margin-bottom: 0 !important;

    .profile__info__item__header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .profile__info__item__header__title {
      font-size: 16px;
      font-weight: 700;
      display: flex;
      column-gap: 5px;
      align-items: center;
    }

    .profile__info__item__content {
      display: block;
      width: 100%;

      & > p {
        word-wrap: break-word;
      }
    }

    .profile__info__verification {
      display: flex;
      column-gap: 5px;
      font-size: 14px;
    }

    &.profile__info__item_card {
      width: auto;
      word-wrap: break-word;
    }
  }

  .profile__gallery__item {
    width: 100%;
    flex-shrink: 0;
    padding: 0px 16px;

    .profile__gallery__photo__wrapper {
      height: 100%;
      border-radius: $app-card-border-radius;
      overflow: hidden;
      background-color: rgba(0, 0, 0, 0.4);
      box-shadow: $profile-card-item-shadow;

      & > .actionable-wrapper {
        display: block;
        height: 100%;
        width: 100%;
      }

      & ::v-deep(.profile__gallery__photo) {
        height: 100%;
        width: 100%;
        object-fit: cover;
        object-position: center;
        display: block;
      }
    }
  }

  /* &.is_cld_image .profile__gallery__item {
    .profile__gallery__photo__wrapper ::v-deep(.profile__gallery__photo) {
      object-fit: fill;
    }
  } */

  .profile__card__end {
    background-color: #fafafa;
    width: 100%;
    border-top: 1px solid #ebebeb;
    padding: 20px 16px 20px 16px;
    box-sizing: border-box;
    flex-shrink: 0;
    margin-top: 2rem;

    &__instructions {
      display: block;
      text-align: left;
      font-size: small;
      margin-bottom: 1rem;

      & > svg {
        display: inline;
        vertical-align: middle;

        &:nth-child(1) {
          height: 13px;
        }
        &:nth-child(2) {
          height: 13px;
        }
      }
    }

    &__report {
      display: inline-flex;
      text-align: left;
      font-size: small;
      align-items: center;
      color: $primary-color;
      border-bottom: 1px solid $primary-color;

      & > svg {
        margin-right: 5px;
        height: 13px;
      }
    }
  }
}

@media only screen and (min-width: 375px) {
  .profile__card {
    .profile__card__avatar .profile__avatar__photo__wrapper,
    .profile__gallery__item .profile__gallery__photo__wrapper {
      min-height: 468px;
    }
    .profile__gallery__item {
      min-height: 468px;
    }
  }
}

@media only screen and (min-width: 412px) {
  .profile__card {
    .profile__card__avatar .profile__avatar__photo__wrapper,
    .profile__gallery__item .profile__gallery__photo__wrapper {
      min-height: 491px;
    }
    .profile__gallery__item {
      min-height: 491px;
    }
  }
}

@media only screen and (min-width: 430px) {
  .profile__card {
    max-width: 430px;
    margin-left: auto;
    margin-right: auto;

    .profile__card__avatar .profile__avatar__photo__wrapper,
    .profile__gallery__item .profile__gallery__photo__wrapper {
      min-height: 538px;
    }
    .profile__gallery__item {
      min-height: 538px;
    }
  }
}
</style>
