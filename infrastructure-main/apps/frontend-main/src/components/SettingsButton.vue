<script setup>
import bus from "@/bus";
import { useAuthStore } from "@/stores/auth";
import { addDays } from "@/utils/helpers";
import { computed } from "vue";
import FiltersIcon from "@/assets/icons/filters.svg";

const authStore = useAuthStore();

const isNewUser = computed(() => {
  return new Date() < addDays(authStore.user.created_at, 2);
});
const openFilters = () => {
  bus.global.emit("modal.overlay:open", { name: "discovery-settings" });
};
</script>

<template>
  <div class="button" @click="openFilters">
    <FiltersIcon class="filters__icon" />
    <span v-if="isNewUser" class="filters__icon__notify"></span>
  </div>
</template>
<style lang="scss" scoped>
.button {
  /* height: 40px;
  width: 40px; */
  /* border-radius: 50%; */
  display: flex;
  position: relative; // Add relative to make notification dot in relation to button
  justify-content: center;
  align-items: center;
  border: none;
  background-color: white;
  /* box-shadow: $profile-card-item-shadow-lg; */
}
.filters__icon {
  height: 24px;
  width: 24px;

  &__notify {
    position: absolute;
    height: 8px;
    width: 8px;
    z-index: 1;
    border-radius: 50%;
    background-color: $primary-color;
    top: 7px;
    right: 7px;
  }
}
</style>
