<script setup>
import User<PERSON><PERSON><PERSON> from "@/components/surveys/UserSurvey.vue";
import { useProfilesStore } from "@/stores/profiles";
import bus from "@/bus";

const profiles = useProfilesStore();

const props = defineProps({
  survey: {
    type: Object,
    required: true,
  },
});

const surveyAnswered = (data) => {
  profiles.surveyCardAnswered(props.survey).finally(() => {
    // Only show the credit notification if the survey is marked as paid
    if (data.isPaid) {
      bus.global.emit("notification.top:show", {
        type: "success",
        title: "Thank you for answering! 😊",
        message: "Your free credits will be topped up shortly!",
        duration: 5000,
      });
    } else {
      // Show a different notification for non-paid surveys
      bus.global.emit("notification.top:show", {
        type: "success",
        title: "Thank you for answering! 😊",
        duration: 5000,
      });
    }
  });
};

// Handle when a survey is dismissed (skipped)
const surveyDismissed = () => {
  profiles.surveyCardDismissed(props.survey);
  // No notification needed for dismissal
};
</script>

<template>
  <div class="survey__card">
    <UserSurvey
      :survey-id="props.survey.id"
      mode="fullscreen"
      @answered="(data) => surveyAnswered(data)"
      @dismissed="() => surveyDismissed()"
    />
  </div>
</template>

<style lang="scss" scoped>
.survey__card {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 1rem;
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
</style>
