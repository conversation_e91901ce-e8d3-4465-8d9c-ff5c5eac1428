<script setup>
import { useRouter } from "vue-router";
import ArrowLeftIcon from "@/assets/icons/arrows/left.svg";
import ButtonSquare from "@/components/buttons/ButtonSquare.vue";

const router = useRouter();
const goBack = () => {
  router.back();
};
</script>

<template>
  <ButtonSquare class="go-back-wrapper" @click="goBack">
    <ArrowLeftIcon />
  </ButtonSquare>
</template>

<style lang="scss" scoped>
.go-back-wrapper {
  min-width: 42px;
  min-height: 42px;
  border: none;
  border-radius: 50%;
  box-shadow: 0 2px 18px 1px rgba(0, 0, 0, 0.1);
  svg {
    width: 10px;
  }
}
</style>
