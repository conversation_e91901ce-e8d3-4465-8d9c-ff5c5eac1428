<template>
  <div
    class="wrapper"
    :class="{
      small: props.source === 'message',
      'profile-action': props.source === 'profile-action',
    }"
    @click="gift"
  >
    <img src="@/assets/icons/gift.png" />
    <span v-if="props.source === 'action'">Gift</span>
  </div>
</template>

<script setup>
import { onBeforeUnmount, onMounted } from "vue";
import bus from "@/bus";

import { useGiftsStore } from "@/stores/gifts";
import { useProfilesStore } from "@/stores/profiles";

import { scrollToTop } from "@/utils/helpers";

const emit = defineEmits(["gifted"]);

const props = defineProps({
  profile: {
    type: Object,
    default: null,
  },
  source: {
    type: String,
    required: true,
    validator: (value) => {
      return ["action", "message", "profile-action"].includes(value);
    },
  },
});

const giftsStore = useGiftsStore();
const profilesStore = useProfilesStore();

const gift = () => {
  giftsStore.setActiveProfile(props.profile);
  bus.global.emit("modal.bottom:open", { name: "gift.purchase" });
};

const onGiftBought = (gift) => {
  bus.global.emit("modal.bottom:close", { name: "gift.purchase" });
  if (["profile-action", "action"].includes(props.source)) {
    scrollToTop();
    profilesStore.removeProfile(props.profileId, "slideRight");
  }
  emit("gifted", gift);
};

onMounted(() => {
  bus.global.on("gift-bought", onGiftBought);
  giftsStore.setActiveSource(props.source);
});

onBeforeUnmount(() => {
  bus.global.off("gift-bought");
  giftsStore.setActiveProfile(null);
  giftsStore.setActiveSource(null);
});
</script>

<style lang="scss" scoped>
.wrapper {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  background-color: #ffffff;

  &.small > img {
    height: 22px;
    width: 22px;
  }

  &.profile-action {
    height: 40px;
    width: 40px;
    border-radius: 40px;
    box-shadow: $profile-card-item-shadow-lg;

    & > img {
      width: 28px;
      height: 28px;
      position: relative;
      left: -1px;
    }
  }
}
</style>
