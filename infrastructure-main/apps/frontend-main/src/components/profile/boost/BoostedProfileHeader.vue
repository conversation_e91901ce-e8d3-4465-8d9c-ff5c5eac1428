<template>
  <div class="hot-profile-card">
    <div class="content">
      <div class="title">
        <span class="icon">🔥</span>
        <span class="text">Trending</span>
        <span class="icon">🔥</span>
      </div>
      <div class="description">
        <p>Instachat this person for <span class="free">free!</span></p>
        <p>
          Send them a message and get
          <span class="credit">{{ boostPackage?.reward_value }} credit</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

defineProps({
  boostPackage: {
    type: Object,
    default: () => ({}),
  },
});
</script>

<style lang="scss" scoped>
.hot-profile-card {
  width: 100%;
  background: linear-gradient(45deg, #fc466b, #3f5efb); // Gradient background
  padding: 16px;
  color: white;
  position: relative;
  text-align: center;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);

  .header {
    top: 8px;
    right: 8px;

    .skip {
      font-size: 12px;
      color: white;
      text-decoration: underline;
      cursor: pointer;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 5px;
      font-size: 20px;
      font-weight: bold;

      .icon {
        font-size: 24px;
      }

      .text {
        text-transform: capitalize;
      }
    }

    .description {
      margin-top: 5px;
      font-size: 14px;
      line-height: 1.5;

      .free {
        font-weight: bold;
      }

      .credit {
        font-weight: bold;
      }
    }
  }
}
</style>
