<template>
  <div class="update-card">
    <div v-if="isLoading" class="update-card__loading">
      <div class="update-card__loading__spinner"></div>
    </div>

    <template v-else>
      <!-- Render the appropriate card based on the property ID -->
      <UpdateCardVerification
        v-if="props.property.id === 'verification'"
        :profile="profileStore.profile"
        @navigate="goToVerification"
        @skip="skipPropertyUpdate"
      />

      <!-- Bio card -->
      <UpdateCardBio
        v-else-if="props.property.id === 'bio'"
        @skip="skipPropertyUpdate"
        @done="donePropertyUpdate"
      />

      <!-- Interests card -->
      <UpdateCardInterests
        v-else-if="props.property.id === 'interests'"
        @done="donePropertyUpdate"
        @skip="skipPropertyUpdate"
      />

      <UpdateCardPushPermission
        v-else-if="props.property.id === 'push-permission'"
        @done="donePropertyUpdate"
        @skip="skipPropertyUpdate"
      />

      <!-- Prompts card (Default card) -->
      <UpdateCardDefault
        v-else-if="props.property.id === 'prompts'"
        @navigate="donePropertyUpdate"
        @skip="skipPropertyUpdate"
      />
    </template>
    <!-- Verification Modal -->
    <ModalOverlay name="profile.verification">
      <ProfileAccountVerificationFingers @close="closeVerificationModal" />
    </ModalOverlay>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { useProfileStore } from "@/stores/profile";
import { useProfilesStore } from "@/stores/profiles";
import { scrollToTop } from "@/utils/helpers";
import bus from "@/bus";

import UpdateCardVerification from "./cards/UpdateCardVerification.vue";
import UpdateCardBio from "./cards/UpdateCardBio.vue";
import UpdateCardInterests from "./cards/UpdateCardInterests.vue";
import UpdateCardPushPermission from "./cards/UpdateCardPushPermission.vue";
import UpdateCardDefault from "./cards/UpdateCardDefault.vue";
import ModalOverlay from "@/components/ModalOverlay.vue";
import ProfileAccountVerificationFingers from "@/components/ProfileAccountVerificationFingers.vue";

import { logEvent } from "@/utils/tracking.js";
import trackedEvents from "@/utils/trackedEvents";

const props = defineProps({
  property: {
    type: Object,
    required: true,
  },
});

const profileStore = useProfileStore();
const profilesStore = useProfilesStore();
const isLoading = ref(true);

// Fetch profile and properties if not already loaded
onMounted(async () => {
  isLoading.value = true;
  try {
    if (!profileStore.profile) {
      await profileStore.fetchProfile();
    }
    logEvent(trackedEvents.UPDATE_CARD_SEEN, {
      type: props.property.id,
    });
  } catch (error) {
    console.error("Error loading property data:", error);
  } finally {
    isLoading.value = false;
  }
});

// Unified function to advance to the next card
const advanceToNextCard = (action = "skip") => {
  // Determine the animation type based on the action
  const animationType = action === "skip" ? "slideLeft" : "slideRight";

  // Remove the current card from the discovery feed and advance to the next one
  profilesStore.removeUpdateCard(props.property.id, animationType);
  scrollToTop();
};

// Skip to next card - wrapper for backward compatibility
const skipPropertyUpdate = () => {
  advanceToNextCard("skip");
  logEvent(trackedEvents.UPDATE_CARD_SKIPPED, {
    type: props.property.id,
  });
};

// Done with property update - wrapper for backward compatibility
const donePropertyUpdate = () => {
  advanceToNextCard("done");
  logEvent(trackedEvents.UPDATE_CARD_ANSWERED, {
    type: props.property.id,
  });
};

// Navigate directly to verification flow without removing the card
const goToVerification = () => {
  // Open the verification modal without removing the card
  // The card will be removed when the verification flow is completed
  bus.global.emit("modal.overlay:open", { name: "profile.verification" });
};

// Close the verification modal
const closeVerificationModal = () => {
  bus.global.emit("modal.overlay:close", { name: "profile.verification" });
};
</script>

<style lang="scss" scoped>
@import "./styles/update-card.scss";

.update-card {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 1rem;
  background: #fff;
  display: flex;
  flex-direction: column;

  &__loading {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    &__spinner {
      @include loading-spinner;
    }
  }
}
</style>
