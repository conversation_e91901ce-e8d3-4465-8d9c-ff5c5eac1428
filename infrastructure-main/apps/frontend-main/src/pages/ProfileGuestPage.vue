<template>
  <ModalOverlay name="match-notification">
    <MatchNotification @close="closeMatchNotification" />
  </ModalOverlay>
  <ModalChatActions name="reportAreYouSure">
    <ReportModal :profile="profile" @reported="handleProfileAction" />
  </ModalChatActions>
  <MainLayout class="guest-page">
    <div v-if="loading || !profile">
      <PreloaderHeart />
    </div>
    <div v-if="!loading && profile" class="profile">
      <ProfileCard
        :profile="profile"
        :show-instachat="false"
        @action="handleProfileAction"
      >
        <template #header>
          <NavigationTop class="profile__top">
            <BackButton />
          </NavigationTop>
        </template>
      </ProfileCard>
    </div>
  </MainLayout>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import bus from "@/bus";
import moment from "moment";

import MainLayout from "@/layouts/MainLayout.vue";
import ModalOverlay from "@/components/ModalOverlay.vue";
import NavigationTop from "@/components/NavigationTop.vue";
import MatchNotification from "@/components/MatchNotification.vue";
import PreloaderHeart from "@/components/PreloaderHeart.vue";
import BackButton from "@/components/buttons/BackButton.vue";
import ProfileCard from "@/components/ProfileCard.vue";
import ReportModal from "@/components/ReportModal.vue";
import ModalChatActions from "@/components/ModalChatActions.vue";

import { useProfilesStore } from "@/stores/profiles";
import { useInitialize } from "@/composables/useInitialize";

import trackedEvents from "@/utils/trackedEvents";
import { logEvent } from "@/utils/tracking.js";

useInitialize();
const route = useRoute();
const router = useRouter();
const profilesStore = useProfilesStore();

const loading = ref(false);
const profile = ref(null);
let lastSeenAtUpdateInterval;

onMounted(() => {
  loading.value = true;

  profilesStore
    .getUserById(route.params.id, false)
    .then((profileData) => {
      profile.value = profileData.user;
    })
    .finally(() => {
      loading.value = false;
      logEvent(trackedEvents.PROFILE_VIEWED, {
        user_id: profile.value.id,
        verified: profile.value.person_verification,
        number_of_images: 1 + profile.value.gallery.length,
        description_length: profile.value.description.length,
        profile_actionable: profile.value.actionable || false,
      });
    });
  // once 5 seconds rewrite last_seen_at to the same value to fire recomputation of last_seen_at
  setInterval(() => {
    if (profile.value?.last_seen_at) {
      profile.value.last_seen_at = moment.utc(
        profile.value.last_seen_at,
        false
      );
    }
  }, 5000);
});

onUnmounted(() => {
  if (lastSeenAtUpdateInterval) {
    clearInterval(lastSeenAtUpdateInterval);
  }
});

const closeMatchNotification = () => {
  logEvent("profile_guest_page_actions_close_match");
  bus.global.emit("modal.overlay:close", {
    name: "match-notification",
  });
};

const handleProfileAction = (isDislike) => {
  profile.value.actionable = false;
  if (isDislike) {
    router.push({ name: "wholikedme" });
  }
};
</script>

<style lang="scss" scoped>
@import "@/styles/user_profile.scss";
.profile {
  padding-top: 1rem;

  &__top {
    top: 0;
    padding: 10px 16px;
    position: absolute;
  }
}
</style>
