<script setup>
import {
  inject,
  reactive,
  onMounted,
  watch,
  onUnmounted,
  ref,
  computed,
} from "vue";
import { useRouter } from "vue-router";
import moment from "moment";
import bus from "@/bus";

import { fetchWhoLikedMeFeed } from "@/api/repositories/discovery";

import { useProfileBoostsStore } from "@/stores/profile_boosts.js";
import { useProfileStore } from "@/stores/profile.js";

import { useInfiniteScroll } from "@vueuse/core";

import { useInitialize } from "@/composables/useInitialize";

import MainLayout from "@/layouts/MainLayout.vue";

import PreloaderHeart from "@/components/PreloaderHeart.vue";
import PreloaderSpinner from "@/components/PreloaderSpinner.vue";
import ButtonMain from "@/components/buttons/ButtonMain.vue";
import FeedCard from "@/components/FeedCard.vue";
import HeaderSection from "@/components/HeaderSection.vue";
import TryBoostingHelper from "@/components/TryBoostingHelper.vue";
import RequestPushAccess from "@/components/RequestPushAccess.vue";
import EndOfList from "@/components/EndOfList.vue";
import ModalOverlay from "@/components/ModalOverlay.vue";
import DiscoverySettings from "@/components/DiscoverySettings.vue";

import CreditsIcon from "@/assets/icons/credits.svg";
import DiscoveryActiveIcon from "@/assets/icons/navigation/discovery_active.svg";
import HeartInCircleIcon from "@/assets/icons/heart-in-circle.svg";

import { logEvent } from "@/utils/tracking.js";
import trackedEvents from "@/utils/trackedEvents";
import SettingsButton from "@/components/SettingsButton.vue";
import { checkIfExceedsClientHeight } from "@/utils/helpers";
useInitialize();
const router = useRouter();
const feedConfirm = inject("feed.confirm");
const likesList = ref(null);
const canScrollLikes = ref(false);
const profileBoostStore = useProfileBoostsStore();
const profileStore = useProfileStore();
let likeTimeUpdateInterval;

const data = reactive({
  loading: false,
  boost: profileBoostStore.getBiggestBoost(),
  hasActiveBoost: profileBoostStore.hasActiveBoost(),
  match: undefined,
  likesDate: "Today",
  profilesWhoLikedMe: [],
  page: 1,
  hasMorePages: true,
});

const showTryBoosting = computed(() => !data.hasActiveBoost && !!data.boost);

const openPaidShowUserConfirmation = (actionID) => {
  // Find the action object by actionID
  const action = data.profilesWhoLikedMe.find(
    (action) => action.actionId === actionID
  );

  feedConfirm(actionID, action)
    .then((response) => {
      // Update the local data with the revealed user
      data.profilesWhoLikedMe = data.profilesWhoLikedMe.map((action) => {
        if (action.actionId === actionID) {
          return { ...action, user: response.user };
        }
        return action;
      });

      // No need for a success notification since we're redirecting to the profile
      // The FeedRevealConfirmation component now handles the navigation
    })
    .catch(() => {
      bus.global.emit("notification.top:show", {
        type: "error",
        message: "Action failed, try again",
      });
    });
};

async function fetchProfiles() {
  try {
    data.loading = true;
    const response = await fetchWhoLikedMeFeed(
      data.page,
      !profileStore.isUsingFiltersOnWhoLikedMeTab
    );
    if (response.likes.length) {
      data.profilesWhoLikedMe.push(...response.likes);
    }
    ++data.page;
    data.hasMorePages = response.hasMorePages;
  } catch (e) {
    console.error("Error fetching profiles:", e);
    bus.global.emit("notification.top:show", {
      type: "error",
      message: "Profiles loading failed, try again",
    });
  } finally {
    data.loading = false;
    if (likesList.value) {
      canScrollLikes.value = checkIfExceedsClientHeight(likesList.value);
    }
  }
}

const redirectToDiscovery = () => {
  logEvent("who_liked_me_page_search_profiles", { page: "likes" });
  router.push({ name: "discovery" });
};

const noProfiles = computed(() => data.profilesWhoLikedMe.length === 0);

// Check if an action is already revealed
const isRevealed = (action) => {
  return action.user && !("revealedLike" in action.user);
};

const countLikes = () => {
  const likeCounts = {
    revealedLikes: 0,
    unrevealedLikes: 0,
  };

  const count = (action) => {
    if (!action.user) return;

    if (!("revealedLike" in action.user)) {
      likeCounts.revealedLikes += 1;
    } else {
      likeCounts.unrevealedLikes += 1;
    }
  };

  data.profilesWhoLikedMe.forEach(count);

  return likeCounts;
};
let resetFunction;

onMounted(async () => {
  await fetchProfiles();
  const { revealedLikes, unrevealedLikes } = countLikes();
  logEvent(trackedEvents.LIKES_OPENED, {
    page: "likes2",
    number_of_revealed_likes: revealedLikes,
    number_of_hidden_likes: unrevealedLikes,
  });
  const { reset } = useInfiniteScroll(likesList, fetchProfiles, {
    distance: 25,
    interval: 250,
    canLoadMore: () => data.hasMorePages,
  });
  resetFunction = reset;
  likeTimeUpdateInterval = setInterval(() => {
    data.profilesWhoLikedMe = data.profilesWhoLikedMe.map((action) => {
      return { ...action, created_at: moment.utc(action?.created_at, false) };
    });
  }, 10000);
  if (profileBoostStore.boostPackages?.length < 1) {
    await profileBoostStore.fetchBoostPackages();
  }
});

onUnmounted(() => {
  if (likeTimeUpdateInterval) {
    clearInterval(likeTimeUpdateInterval);
  }
});

const filtersUpdated = () => {
  data.profilesWhoLikedMe = [];
  data.page = 1;
  resetFunction();
  fetchProfiles();
};
watch(profileBoostStore.boostPackages, (v) => {
  data.boost = v;
});
watch(profileStore.isUsingFiltersOnWhoLikedMeTab, filtersUpdated);

const canLoadNativeRequestPush = window.SababuuNativeBridge !== undefined;
</script>

<template>
  <RequestPushAccess v-if="canLoadNativeRequestPush" loaded-from="likes2" />
  <ModalOverlay :closable="false" name="discovery-settings" :padding-s-m="true">
    <DiscoverySettings @updated="filtersUpdated" />
  </ModalOverlay>
  <main-layout>
    <template #header>
      <HeaderSection>
        <h1>Liked You</h1>
        <SettingsButton />
      </HeaderSection>
    </template>
    <article
      class="who-liked-me"
      :class="{ 'who-liked-me-article__no-likes': noProfiles }"
    >
      <PreloaderHeart
        v-if="data.loading && noProfiles"
        text="Loading likes ..."
      />

      <section v-else-if="noProfiles" class="who-liked-me__no-likes">
        <HeartInCircleIcon
          v-if="!showTryBoosting"
          class="who-liked-me__no-likes__heart-img"
        />
        <p v-if="!showTryBoosting" class="dialogs__no-dialogs__description">
          You don't have any new likes ... yet 😉<br />You can continue to like
          profiles.
        </p>
        <button-main
          v-if="!showTryBoosting"
          class="main-btn"
          @click="redirectToDiscovery"
          >View Profiles</button-main
        >
        <TryBoostingHelper v-else :boost="data.boost" page="likes" />
      </section>

      <section
        v-else
        id="scrollContainer"
        ref="likesList"
        class="who-liked-me__users"
      >
        <div class="who-liked-me__users-list-explainer">
          See who liked you instantly for
          <b>1 <CreditsIcon class="inline-icon" /> credit!</b> Or, browse
          profiles in
          <b><DiscoveryActiveIcon class="inline-icon" /> Discover</b> and if you
          match, seeing them is always free.
        </div>
        <section class="who-liked-me__feed-container">
          <FeedCard
            v-for="(action, index) in data.profilesWhoLikedMe"
            :key="index"
            :action="action"
            :type="isRevealed(action) ? 'revealed' : 'short'"
            @reveal="openPaidShowUserConfirmation"
          />
        </section>
        <EndOfList
          v-if="
            canScrollLikes &&
            !data.hasMorePages &&
            !data.loading &&
            data.profilesWhoLikedMe.length > 0
          "
          description="No more likes"
        />
        <div
          v-if="data.loading && !noProfiles"
          class="who-liked-me__users__preloader-spinner-wrap"
        >
          <PreloaderSpinner :dark="true" />
        </div>
      </section>
    </article>
  </main-layout>
</template>

<style lang="scss" scoped>
//section 1
.who-liked-me {
  width: 100%;
  height: 100%;

  display: flex;
  flex-direction: column;
}

.who-liked-me-article__no-likes {
  gap: 0px;
}

.who-liked-me__top-menu {
  gap: 30px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.who-liked-me__row {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

h1 {
  span {
    color: #e94057;
  }
}

.who-liked-me__top-menu__description {
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%; /* 24px */
  color: rgba(0, 0, 0, 0.7);
}

.who-liked-me__top-menu__note {
  color: rgba(0, 0, 0, 0.7);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%; /* 18px */
}
span.who-liked-me__note {
  padding: 0 8px;
}
hr {
  width: 100%;
  max-width: 100%;
  height: 1px;
  flex-shrink: 0;
  color: #e8e6ea;
  @media screen and (max-width: 360px) {
    max-width: 80px;
  }
}

//section 2
.who-liked-me__no-likes {
  position: relative;
  padding: 200px 16px 0 16px;

  gap: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.who-liked-me__no-likes__heart-img {
  top: 30px;
  position: absolute;
}

//section 3
.who-liked-me__users {
  gap: 15px;
  width: 100%;

  overflow-y: scroll;

  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  position: relative;
}
.who-liked-me__users-list-explainer {
  text-align: left;
  font-size: 0.875rem;
  margin: 8px 0 8px 0;
  .inline-icon {
    display: inline-block;
    width: 16px;
  }
}

.who-liked-me__feed-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.who-liked-me__users__preloader-spinner-wrap {
  width: 100%;
  padding: 20px 0;

  display: flex;
  align-items: center;
  flex-direction: column;
}

.who-liked-me__no-likes__description {
  text-align: center;
  margin-bottom: 10px;
}
</style>
