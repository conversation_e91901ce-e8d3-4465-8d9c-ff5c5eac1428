import { createRouter, createWebHistory } from "vue-router";
import routes from "@/router/routes";
import { socket } from "@/plugins/socket.js";

import { useAuthStore } from "@/stores/auth";
import { useBalanceStore } from "@/stores/balance";
import { useNotifyStore } from "@/stores/notify";
import { useMessagesStore } from "@/stores/messages";

import NotFoundPage from "@/pages/NotFoundPage.vue";
import { useDialogsStore } from "@/stores/dialogs";
import { useUserPresenceStore } from "@/stores/userPresence";
import { fetchNotificationIndicators } from "@/api/repositories/user";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: routes.concat([
    {
      path: "/:catchAll(.*)",
      name: "PageNotExist",
      component: NotFoundPage,
      meta: {
        public: true,
      },
    },
  ]),
  scrollBehavior(to) {
    if (to.hash) {
      return {
        el: to.hash,
        behavior: "smooth",
      };
    }
    return { top: 0 };
  },
});

const getFromName = (from) => {
  if (!from.name) {
    return "discovery";
  } else if (from.name === "profile.guest") {
    return { path: from.fullPath };
  } else {
    return from.name;
  }
};

router.beforeEach(async (to, from, next) => {
  window.document.title = import.meta.env.VITE_APP_TITLE;
  to.meta.goBack = getFromName(from);

  // Save UTM parameters to local storage or Vuex store
  if (to.query.utm_source) {
    const { utm_source, utm_medium, utm_campaign } = to.query;
    localStorage.setItem("utm_source", utm_source);

    if (utm_medium) localStorage.setItem("utm_medium", utm_medium || "");
    if (utm_campaign) localStorage.setItem("utm_campaign", utm_campaign || "");
  }

  const onlyGuests = to.matched.some((record) => record.meta.onlyGuests);
  const isPublic = to.matched.some((record) => record.meta.public);

  const authStore = useAuthStore();
  const notifyStore = useNotifyStore();

  if (to.query.reset) {
    authStore.resetUser();
  }

  if (!isPublic && !authStore.isAuthenticated) {
    next({
      path: "/auth/login",
      query: { redirect: to.fullPath },
    });
    return;
  }

  if (authStore.isAuthenticated && !authStore.isUserInitialized()) {
    await authStore.updateUserFromApi();
  }

  const dialogsStore = useDialogsStore();
  if (authStore.isAuthenticated && !dialogsStore.isDialogsListenerInitialized) {
    socket()
      .private("User." + authStore.getUser().id)
      .listen(".Dialogs", function (payload) {
        dialogsStore.updateDialog(payload.dialog);
      });
    dialogsStore.isDialogsListenerInitialized = true;
  }
  const messagesStore = useMessagesStore();
  if (authStore.isAuthenticated && !messagesStore.isHandlerInitialized) {
    socket()
      .private("User." + authStore.getUser().id)
      .listen(".Message", function (payload) {
        messagesStore.setNewMessage(payload);
      });
    messagesStore.setHandlerInitialized();
  }

  const balanceStore = useBalanceStore();
  if (authStore.isAuthenticated && !balanceStore.isBalanceInitialized) {
    socket()
      .private("User." + authStore.getUser().id)
      .listen(".WalletBalanceUpdated", function (payload) {
        balanceStore.setNewBalance(payload.wallet.credits);
      });
    balanceStore.setBalanceInitialized();
  }

  if (authStore.isAuthenticated && !notifyStore.isNotificationsInitialized) {
    const notificationIndicators = await fetchNotificationIndicators();
    notifyStore.setNotifications({
      new_messages_notifications: notificationIndicators.new_messages_count,
      new_likes_notifications: notificationIndicators.new_likes_count,
      new_matches_notifications: notificationIndicators.new_matches_count,
    });
    socket()
      .private("User." + authStore.getUser().id)
      .listen(".NotificationsIndicators", function (payload) {
        const notifications = {
          new_messages_notifications: payload.new_messages_notifications,
          new_likes_notifications: payload.new_likes_notifications,
          new_matches_notifications: payload.new_matches_notifications,
        };
        notifyStore.setNotifications(notifications);
      });

    notifyStore.setNotificationsInitialized();
  }
  const userPresenceStore = useUserPresenceStore();
  if (
    authStore.isAuthenticated &&
    !userPresenceStore.isUserPresenceListenerInitialized
  ) {
    socket()
      .join("Users")
      .here(function (users) {
        userPresenceStore.setOnlineUsersIds(users.map((user) => user.id));
      })
      .joining(function (user) {
        if (user.id) userPresenceStore.addOnlineUserId(user.id);
      })
      .leaving(function (user) {
        if (user.id) userPresenceStore.removeOnlineUserId(user.id);
      })
      .error((error) => {
        console.error(error);
      });
    userPresenceStore.isUserPresenceListenerInitialized = true;
  }

  if (authStore.isAuthenticated && !authStore.isRegisterCompleted()) {
    if (!to.name.includes("auth.signup")) {
      next({ name: "auth.signup" });
      return;
    }
    next();
    return;
  }

  if (
    authStore.isAuthenticated &&
    authStore.isRegisterCompleted() &&
    to.name.includes("auth")
  ) {
    next({ name: "discovery" });
    return;
  }

  if (onlyGuests && authStore.isAuthenticated) {
    if (to.name === "landing") {
      next({
        name: "discovery",
      });
      return;
    } else {
      next("");
      return;
    }
  }

  next();
});

// inspired by https://stackoverflow.com/a/77828857
let direction = "";
router.options.history.listen((to, from, info) => {
  direction = info.direction;
});

router.beforeEach((to, from, next) => {
  const isBackNavigation = direction === "back";
  direction = "";

  if (
    isBackNavigation &&
    (from.name === "discovery" ||
      from.name === "online" ||
      from.name === "wholikedme" ||
      from.name === "dialogs")
  ) {
    next(false);
  } else {
    next();
  }
});

window.handleNativeBackPress = () => {
  router.back();
};

window.handleNativeRouteChange = (nativeRouteTrigger) => {
  try {
    const url = new URL(nativeRouteTrigger);
    if (url.pathname) router.push(url.pathname);
  } catch (ex) {
    /* Ignore */
  }
};

export default router;
