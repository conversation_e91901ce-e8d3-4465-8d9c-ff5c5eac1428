import { ref, computed } from "vue";
import { defineStore } from "pinia";
import { resetPosthogUser } from "@/plugins/posthog";
import { useProfilesStore } from "@/stores/profiles.js";
import { useCountryStore } from "./country";
import { isVodacomTanzaniaNumber } from "@/utils/helpers.js";
import { useProfileStore } from "./profile";
import { fetchNotificationIndicators, fetchUserData } from "@/api/repositories/user";
import { useDialogsStore } from "./dialogs";
import { useNotifyStore } from "./notify";
import { useSurveysStore } from "./surveys";

export const useAuthStore = defineStore(
  "auth",
  () => {
    const { resetProfiles } = useProfilesStore();
    const notifyStore = useNotifyStore();
    const { resetProfile } = useProfileStore();
    const { resetSurveys } = useSurveysStore();
    const { resetDialogs } = useDialogsStore();
    const { setCountryData } = useCountryStore();
    const token = ref(localStorage.getItem("token") || undefined);
    const user = ref({});
    const completeRegisterData = ref({
      phoneNumber: "",
      name: "",
      gender: "",
      birthday: "",
      description: "",
      location: null,
      latitude: null,
      longitude: null,
      gallery: [],
      /*
       * The attributes below are
       * used to control UI state
       */
      onboardingStep: "phoneNumber",
      initialAvatarImage: undefined,
      avatar: undefined,
      initialGalleryImage: undefined,
      galleryImage: undefined,
    });
    const isFromGhana = computed(() => {
      return user.value.country === "ghana";
    });
    const setToken = (newToken) => {
      token.value = newToken;
      localStorage.setItem("token", newToken);
    };

    const getToken = () => {
      return token.value;
    };

    const isAuthenticated = computed(() => {
      return !!token.value;
    });

    const setUser = (userData) => {
      user.value = { ...user.value, ...userData };
    };

    const resetUser = () => {
      user.value = undefined;
    };

    const resetAuth = () => {
      resetUser();
      resetToken();
      resetCompleteRegisterData();
    };

    const resetToken = () => {
      token.value = undefined;
      localStorage.removeItem("token");
    };

    const resetCompleteRegisterData = () => {
      completeRegisterData.value = {
        name: "",
        onboardingStep: "phoneNumber",
        birthday: "",
        gender: "",
        avatar: "",
        description: "",
        city: "",
        gallery: [],
        initialAvatarImage: undefined,
        initialGalleryImage: undefined,
        galleryImage: undefined,
      };
    };

    const setUserPhoneIsVerified = (isVerified = true) => {
      user.value.phone_verified = isVerified;
    };

    const setUserRegisterIsCompleted = () => {
      user.value.register_completed = true;
    };

    const getUser = () => {
      return user.value;
    };

    const isUserInitialized = () => {
      return user.value !== undefined;
    };

    const isPhoneVerified = () => {
      return user.value && user.value.phone_verified;
    };

    const isRegisterCompleted = () => {
      return user.value && user.value.register_completed;
    };

    const isPushNotificationSubscribed = computed(() => {
      return user.value && user.value.push_subscribed;
    });

    const isVodacomTanzaniaUser = computed(() => {
      return user.value && isVodacomTanzaniaNumber(user.value.phone);
    });

    const getCompleteRegisterData = () => {
      return completeRegisterData.value;
    };

    const setCompleteRegisterData = (data) => {
      completeRegisterData.value = { ...completeRegisterData.value, ...data };
    };

    const logout = () => {
      resetToken();
      resetUser();
      resetProfile();
      resetCompleteRegisterData();
      resetDialogs();
      resetPosthogUser();
      resetSurveys();
      resetProfiles([]);
      setCountryData(undefined);
      if (window.SababuuNativeBridge?.logout) {
        window.SababuuNativeBridge.logout();
      }
    };

    const updateUserFromApi = async () => {
      const data = await fetchUserData();
      setUser(data.user);
    };

    return {
      setToken,
      getToken,
      isAuthenticated,
      setUser,
      resetUser,
      resetAuth,
      getUser,
      isUserInitialized,
      isPhoneVerified,
      setUserPhoneIsVerified,
      isRegisterCompleted,
      setUserRegisterIsCompleted,
      getCompleteRegisterData,
      setCompleteRegisterData,
      resetCompleteRegisterData,
      logout,
      updateUserFromApi,
      token,
      user,
      completeRegisterData,
      isPushNotificationSubscribed,
      isVodacomTanzaniaUser,
      isFromGhana,
    };
  },
  { persist: true }
);
