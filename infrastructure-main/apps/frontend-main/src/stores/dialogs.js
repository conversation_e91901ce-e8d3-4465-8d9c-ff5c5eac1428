import { block, remove, showAll } from "@/api/repositories/dialogs";
import { defineStore } from "pinia";
import { ref } from "vue";
import { useAuthStore } from "./auth";
import bus from "@/bus";
import { useRouter } from "vue-router";

/**
 * Filters an array of objects by their IDs, returning a new array with only the unique objects.
 * We're reversing the initial array to ensure that the most recent dialogs are at the beginning of the array.
 *
 * @param {Array} array - The array of objects to filter.
 * @return {Array} The filtered array of objects.
 */
const filterArrayById = (array) => {
  return array
    .reverse()
    .reduce((acc, current) => {
      const x = acc.find((item) => item.id === current.id);
      if (!x) {
        return acc.concat([current]);
      } else {
        return acc;
      }
    }, [])
    .reverse();
};

const DIALOGS_TTL = 15 * 1000; // 15 seconds

export const useDialogsStore = defineStore(
  "dialogs",
  () => {
    const dialogs = ref([]);
    const dialogsLoading = ref(false);
    const dialogsCurrentPage = ref(1);
    const isDialogsListenerInitialized = ref(false);
    const activeDialog = ref(undefined);
    const noMoreDialogs = ref(false);
    const authStore = useAuthStore();
    const router = useRouter();
    const fetchedDialogsTimestamp = ref(0);
    const dialogPageScroll = ref(0);
    const unsentCache = ref({});

    const getDialogs = async (shouldRefresh = false) => {
      if (dialogsLoading.value === true) return;

      if (shouldRefresh) {
        if (Date.now() - fetchedDialogsTimestamp.value < DIALOGS_TTL) {
          return;
        }

        dialogs.value = [];
        dialogsCurrentPage.value = 1;
        noMoreDialogs.value = false;
      }

      if (noMoreDialogs.value) {
        return;
      }

      dialogsLoading.value = true;

      try {
        const { dialogs: newDialogs, hasMorePages } = await showAll(
          dialogsCurrentPage.value
        );

        dialogs.value = filterArrayById([...dialogs.value, ...newDialogs]);
        noMoreDialogs.value = !hasMorePages;
        dialogsCurrentPage.value++;
      } catch (error) {
        noMoreDialogs.value = true;
        throw error;
      } finally {
        dialogsLoading.value = false;
        fetchedDialogsTimestamp.value = Date.now();
      }
    };

    const updateDialog = (dialog) => {
      if (activeDialog.value?.id === dialog.id) {
        // removing notifications cause if the dialog is currently opened we see the latest message.
        // dialog.new_messages_notifications_for_sender = 0;
        // dialog.new_messages_notifications_for_recipient = 0;
        if (dialog.is_blocked) {
          deactivateDialog();
        }
      }

      // if the dialog is not in the list we add it to the beginning
      if (!dialogs.value.find((item) => item.id === dialog.id)) {
        const profile =
          dialog.from.id === authStore.user.id
            ? dialog.to
            : dialog.from;
        dialogs.value.unshift({ ...dialog, profile });
        return;
      }

      dialogs.value = dialogs.value
        .map((item) => {
          return item.id === dialog.id
            ? { ...dialog, user: item.user } // not overriding profile cause it can store the opposite user
            : item;
        })
        .sort(
          (a, b) =>
            new Date(b.last_message.created_at).getTime() -
            new Date(a.last_message.created_at).getTime()
        );
    };

    const blockDialog = async (dialog) => {
      await block(dialog.id);
      dialog.is_blocked = true;
      updateDialog(dialog);
    };

    const removeDialog = async (dialog) => {
      await remove(dialog.id);
      dialogs.value = dialogs.value.filter((item) => item.id !== dialog.id);
    };

    const activateDialog = (dialog) => {
      activeDialog.value = dialog;
    };

    const deactivateDialog = () => {
      router.push({ name: "dialogs" }).then(() => {
        activeDialog.value = undefined;
      });
      bus.global.emit("modal.bottom:close", {
        name: "chat",
      });
    };

    const getDialogByUserId = async (userId) => {
      if (!dialogs.value) {
        await getDialogs();
      }
      return dialogs.value.find((dialog) => {
        return dialog.user.id === userId;
      });
    };

    const resetDialogs = () => {
      dialogs.value = [];
      activeDialog.value = undefined;
    };

    const cacheUnsentMessage = ({ dialogId, message }) => {
      const cachedDialog = unsentCache.value[dialogId] || null;
      if (message.length === 0 && cachedDialog) {
        delete unsentCache.value[dialogId];
      } else {
        unsentCache.value = {
          ...unsentCache.value,
          [dialogId]: message,
        };
      }
    };

    const getCachedMessage = (dialogId) => unsentCache.value[dialogId] || null;

    return {
      dialogs,
      activeDialog,
      dialogsLoading,
      unsentCache,
      getDialogs,
      getDialogByUserId,
      updateDialog,
      dialogsCurrentPage,
      noMoreDialogs,
      blockDialog,
      removeDialog,
      isDialogsListenerInitialized,
      activateDialog,
      deactivateDialog,
      resetDialogs,
      cacheUnsentMessage,
      getCachedMessage,
      dialogPageScroll,
      fetchedDialogsTimestamp,
    };
  },
  {
    persist: {
      paths: [
        "dialogs",
        "dialogPageScroll",
        "dialogsCurrentPage",
        "noMoreDialogs",
        "fetchedDialogsTimestamp",
        "unsentCache",
      ],
    },
  }
);
