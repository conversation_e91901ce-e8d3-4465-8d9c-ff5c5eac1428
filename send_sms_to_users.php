<?php

require_once __DIR__ . '/infrastructure-main/apps/backend-main/vendor/autoload.php';

use AfricasTalking\SDK\AfricasTalking;

// Load environment variables from .env file
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/infrastructure-main/apps/backend-main');
$dotenv->load();

// Configuration
$csvFilePath = $_SERVER['HOME'] . '/Downloads/boosted_users_phones_20250528.csv';
$message = "Hey, this is the CEO of Sababuu. Thank you so much for boosting your profile two weeks ago. We found an issue with your profile that was causing the boost to be less effective. To make up for this, we just gave you another boost for free! It's already active. Enjoy!";

// Africa's Talking credentials
$username = $_ENV['SMS_USERNAME'] ?? null;
$apiKey = $_ENV['SMS_API_KEY'] ?? null;
$from = $_ENV['SMS_FROM'] ?? null;

if (!$username || !$apiKey) {
    die("Error: Africa's Talking credentials not found in environment variables.\n");
}

// Initialize the SDK
$AT = new AfricasTalking($username, $apiKey);

// Get the SMS service
$sms = $AT->sms();

// Check if file exists
if (!file_exists($csvFilePath)) {
    die("Error: CSV file not found at $csvFilePath\n");
}

// Open the CSV file
$file = fopen($csvFilePath, 'r');
if (!$file) {
    die("Error: Unable to open CSV file\n");
}

// Skip header row
fgetcsv($file);

// Initialize counters
$totalUsers = 0;
$successCount = 0;
$failCount = 0;

// Create a log file
$logFile = fopen('sms_sending_log_' . date('Y-m-d_H-i-s') . '.txt', 'w');
fwrite($logFile, "SMS Sending Log - " . date('Y-m-d H:i:s') . "\n");
fwrite($logFile, "Message: $message\n\n");

echo "Starting to send SMS messages...\n";

// Process each row in the CSV
while (($row = fgetcsv($file)) !== false) {
    $phone = $row[0]; // First column contains phone numbers
    $totalUsers++;
    
    echo "Sending to $phone... ";
    
    try {
        // Prepare the SMS parameters
        $options = [
            'to'      => $phone,
            'message' => $message
        ];
        
        // Add the 'from' parameter if it's set
        if ($from) {
            $options['from'] = $from;
        }
        
        // Send the message
        $result = $sms->send($options);
        
        // Check if the message was sent successfully
        if (isset($result['data']->SMSMessageData->Recipients[0]->status) && 
            $result['data']->SMSMessageData->Recipients[0]->status === 'Success') {
            echo "SUCCESS\n";
            $successCount++;
            fwrite($logFile, "$phone: SUCCESS\n");
        } else {
            echo "FAILED\n";
            $failCount++;
            fwrite($logFile, "$phone: FAILED - " . json_encode($result) . "\n");
        }
        
        // Add a small delay to avoid overwhelming the API
        usleep(200000); // 200ms delay
        
    } catch (Exception $e) {
        echo "ERROR: " . $e->getMessage() . "\n";
        $failCount++;
        fwrite($logFile, "$phone: ERROR - " . $e->getMessage() . "\n");
    }
}

// Close files
fclose($file);

// Write summary to log
$summary = "\nSummary:\n";
$summary .= "Total users: $totalUsers\n";
$summary .= "Successfully sent: $successCount\n";
$summary .= "Failed: $failCount\n";

echo $summary;
fwrite($logFile, $summary);
fclose($logFile);

echo "\nSMS sending completed. See log file for details.\n";
